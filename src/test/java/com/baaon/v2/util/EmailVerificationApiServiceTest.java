package com.baaon.v2.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baaon.v2.config.ApiResponse;
import com.baaon.v2.config.EmailVerificationApiService;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.Method;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * EmailVerificationApiService 测试类
 */
public class EmailVerificationApiServiceTest {
    
    private EmailVerificationApiService service;
    
    @Before
    public void setUp() {
        service = new EmailVerificationApiService();
    }
    
    /**
     * 测试从真实邮箱内容中提取验证码
     */
    @Test
    public void testExtractVerificationCodeFromRealEmail() throws Exception {
        // 您提供的真实邮箱内容
        String emailContent = "Received: from e226-12.smtp-out.us-east-2.amazonses.com (*************)\\r\\n        by cloudflare-email.net (unknown) id PkyOePF2YiTe\\r\\n        for <<EMAIL>>; Fri, 11 Jul 2025 12:28:16 +0000\\r\\nARC-Seal: i=1; a=rsa-sha256; s=cf2024-1; d=deepstudy.fun; cv=none;\\r\\n\\tb=SVCHJvYQlU0CjKyJMHaKbEmgpaLkPMx40rGo101gKSDinjP3PUXhj5gqkRLKhFsjv4d6rqguM\\r\\n\\t8we0+Xl4HQQPkfsETNCus9zMpL/uLmY6YyOGvtAyGbCP2M88MhNOk3j7fO1BAonfXx7DXb5YJ0I\\r\\n\\t+n52jN9AHweR8D9YXuJIOtRdxWLexjMml4RWZDEoVvR5e2jYbeMzhT0uerSoRxY/vpYh3M8tIvJ\\r\\n\\traz37o8t7bWnP/zVhewU2cYIUyDm2wV1auxodXuYnIvv6ExeghMomqLo8In6/JwKe+GF+WfPnhv\\r\\n\\t3dB7aCZdkgjxjepP/1i15v3GIvnvzTKolZBvwK0Eya/g==;\\r\\nARC-Message-Signature: i=1; a=rsa-sha256; s=cf2024-1; d=deepstudy.fun; c=relaxed/relaxed;\\r\\n\\th=Date:Subject:To:From:reply-to:cc:resent-date:resent-from:resent-to\\r\\n\\t:resent-cc:in-reply-to:references:list-id:list-help:list-unsubscribe\\r\\n\\t:list-subscribe:list-post:list-owner:list-archive; t=1752236897;\\r\\n\\tx=1752841697; bh=3F7Ew03jXjfyxK2cJUQ3BwhdgR+mrlhDqrHgjJuI46E=; b=gUsq8aKKeX\\r\\n\\tENNYfoKYyyxdAXg48zWgw3i+RYqQUFDXY5VKvQvq/5uk5K7+2rEJTC+2M+K7WBHy1DkjTH+br42\\r\\n\\tezP2HWjF6qUnzgwbA8oOXdnjv3GHB48zZh3Ass0hkut7ktthq3WV9TN6C5ClMoWvwXsAN7XuYGM\\r\\n\\tnAMI1dq0fDQzf71yA2oT7EcMDikD1/0jlG1LwNZVTFw6Y2T7cxgZOPLy0gvz2XpIq8un2leMeG/\\r\\n\\tRWSiinCDIk3s8mU9HGrSQgA+7vDpi0NW5EnpkE/4CBumzDA5GrfQsMF5nA7Rd6setZrzZWmMr0h\\r\\n\\tcjqdULHIPQDPhgbHejjd3CkyNqp7/E5w==;\\r\\nARC-Authentication-Results: i=1; mx.cloudflare.net;\\r\\n\\tdkim=pass header.d=augmentcode.com header.s=szzxjfhwb5p4sftwuiesfqxw57pecatu header.b=A2bznN9c;\\r\\n\\tdkim=pass header.d=amazonses.com header.s=ndjes4mrtuzus6qxu3frw3ubo3gpjndv header.b=uRTOZvnH;\\r\\n\\tdmarc=pass header.from=augmentcode.com policy.dmarc=quarantine;\\r\\n\\tspf=pass (mx.cloudflare.net: <NAME_EMAIL> designates ************* as permitted sender) smtp.helo=e226-12.smtp-out.us-east-2.amazonses.com;\\r\\n\\tspf=pass (mx.cloudflare.net: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;\\r\\n\\tarc=none smtp.remote-ip=*************\\r\\nReceived-SPF: pass (mx.cloudflare.net: <NAME_EMAIL> designates ************* as permitted sender)\\r\\n\\treceiver=mx.cloudflare.net; client-ip=*************; envelope-from=\\\"<EMAIL>\\\"; helo=e226-12.smtp-out.us-east-2.amazonses.com;\\r\\nAuthentication-Results: mx.cloudflare.net;\\r\\n\\tdkim=pass header.d=augmentcode.com header.s=szzxjfhwb5p4sftwuiesfqxw57pecatu header.b=A2bznN9c;\\r\\n\\tdkim=pass header.d=amazonses.com header.s=ndjes4mrtuzus6qxu3frw3ubo3gpjndv header.b=uRTOZvnH;\\r\\n\\tdmarc=pass header.from=augmentcode.com policy.dmarc=quarantine;\\r\\n\\tspf=pass (mx.cloudflare.net: <NAME_EMAIL> designates ************* as permitted sender) smtp.helo=e226-12.smtp-out.us-east-2.amazonses.com;\\r\\n\\tspf=pass (mx.cloudflare.net: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;\\r\\n\\tarc=none smtp.remote-ip=*************\\r\\nDKIM-Signature: v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple;\\r\\n\\ts=szzxjfhwb5p4sftwuiesfqxw57pecatu; d=augmentcode.com;\\r\\n\\tt=1752236896;\\r\\n\\th=From:To:Subject:Message-ID:Date:MIME-Version:Content-Type;\\r\\n\\tbh=3GEOh1JviTvyttk6MQmd0OBNx2n/FD0u4tK4TsT7D6A=;\\r\\n\\tb=A2bznN9cstw9ddoNFTHc+cCVUsy84298q0PtVbk9yWWYHpL0U7JbILAO2IHuDrvZ\\r\\n\\tD9/MrlUR2z0FAIbzyI/42RrMObwXnbxVD/pAjfPKM8BwNzCDVxmoXJ1ywR6EHV+YELk\\r\\n\\tlz0Wmou3MHasGOgiH415Z6rZ0596fea4Qi5gZWW5mzuzeGju+1m9FD6bD+bX3ovjPZK\\r\\n\\tIsB56cB+KhLzd8CL+EDpZPgo+R/4Iphmp5d5zfbS/MjaAPDpsMRTzm5hQreVdwY9Ke/\\r\\n\\tBV1UHIUnYbctel5CAAAU/dCJFvs5K9KZCX4WozqQuavCScx6O7hrp6PK1lBUXvWjYX/\\r\\n\\t5afx82nESA==\\r\\nDKIM-Signature: v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple;\\r\\n\\ts=ndjes4mrtuzus6qxu3frw3ubo3gpjndv; d=amazonses.com; t=1752236896;\\r\\n\\th=From:To:Subject:Message-ID:Date:MIME-Version:Content-Type:Feedback-ID;\\r\\n\\tbh=3GEOh1JviTvyttk6MQmd0OBNx2n/FD0u4tK4TsT7D6A=;\\r\\n\\tb=uRTOZvnHnl9HutcUSKaEiPCsED4EbDiGQkPB8Njh3OosioMEg3d6HvPUfUq3y1TT\\r\\n\\tpPFw6xzX9mevYlav2lEAS3qu7NQ+RBdYZCXdblo4aHpNUGyZAc4QlAOit+03c3eAeS6\\r\\n\\tkSJCTt6XSLpGTlAQgHfOPkOh9zQS0UuuW4/ALhEc=\\r\\nFrom: Augment Code <<EMAIL>>\\r\\nTo: <EMAIL>\\r\\nSubject: Welcome to Augment Code\\r\\nMessage-ID: <<EMAIL>>\\r\\nDate: Fri, 11 Jul 2025 12:28:16 +0000\\r\\nMIME-Version: 1.0\\r\\nContent-Type: multipart/alternative;\\r\\n boundary=\\\"--_NmP-3446fba16f178292-Part_1\\\"\\r\\nFeedback-ID: ::1.us-east-2.J4j2CSD6w5RgMfPv6svYFoZ7h3dgSPLPQm7eqivBr5A=:AmazonSES\\r\\nX-SES-Outgoing: 2025.07.11-*************\\r\\n\\r\\n----_NmP-3446fba16f178292-Part_1\\r\\nContent-Type: text/plain; charset=utf-8\\r\\nContent-Transfer-Encoding: quoted-printable\\r\\n\\r\\n\\r\\n\\r\\n =20\\r\\n =20\\r\\n =20\\r\\n   =20\\r\\n     =20\\r\\n     =20\\r\\n       =20\\r\\n         =20\\r\\n       =20\\r\\n\\r\\n       =20\\r\\n       =20\\r\\n\\r\\n         =20\\r\\n         =20\\r\\n\\r\\n            Your verification code is: 762843\\r\\n\\r\\n         =20\\r\\n\\r\\n       =20\\r\\n\\r\\n        If you are having any issues with your account, please don't =\\r\\nhesitate to contact us by replying to this mail.\\r\\n\\r\\n       =20\\r\\n        Thanks!\\r\\n       =20\\r\\n\\r\\n        Augment Code\\r\\n\\r\\n       =20\\r\\n       =20\\r\\n       =20\\r\\n          If you did not make this request, you can safely =\\r\\nignore this email. Never share this one-time code with anyone - Augment =\\r\\nsupport will never ask for your verification code. Your account remains =\\r\\nsecure and no action is needed.\\r\\n       =20\\r\\n     =20\\r\\n     =20\\r\\n   =20\\r\\n =20\\r\\n\\r\\n\\r\\n\\r\\n----_NmP-3446fba16f178292-Part_1\\r\\nContent-Type: text/html; charset=utf-8\\r\\nContent-Transfer-Encoding: quoted-printable\\r\\n\\r\\n<!DOCTYPE html PUBLIC \\\"-//W3C//DTD XHTML 1.0 Transitional//EN\\\" \\\"http://www.=\\r\\nw3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\\\">\\r\\n<html xmlns=3D\\\"http://www.w3=\\r\\n.org/1999/xhtml\\\">\\r\\n  <head>\\r\\n    <meta http-equiv=3D\\\"Content-Type\\\" =\\r\\ncontent=3D\\\"text/html; charset=3DUTF-8\\\">\\r\\n    <style type=3D\\\"text/css\\\">.=\\r\\nExternalClass,.ExternalClass div,.ExternalClass font,.ExternalClass p,.=\\r\\nExternalClass span,.ExternalClass td,img{line-height:100%}#outlook =\\r\\na{padding:0}.ExternalClass,.ReadMsgBody{width:100%}a,blockquote,body,li,p,=\\r\\ntable,td{-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}table,=\\r\\ntd{mso-table-lspace:0;mso-table-rspace:0}img{-ms-interpolation-mode:bicubic=\\r\\n;border:0;height:auto;outline:0;text-decoration:none}table{border-collapse:=\\r\\ncollapse!important}#bodyCell,#bodyTable,body{height:100%!=\\r\\nimportant;margin:0;padding:0;font-family:ProximaNova,=\\r\\nsans-serif}#bodyCell{padding:20px}#bodyTable{width:600px}@font-face{font-fa=\\r\\nmily:ProximaNova;src:url(https://cdn.auth0.com/fonts/proxima-nova/proximano=\\r\\nva-regular-webfont-webfont.eot);src:url(https://cdn.auth0.=\\r\\ncom/fonts/proxima-nova/proximanova-regular-webfont-webfont.eot?#iefix) =\\r\\nformat('embedded-opentype'),url(https://cdn.auth0.com/fonts/proxima-nova/pr=\\r\\noximanova-regular-webfont-webfont.woff) format('woff');font-weight:400;font=\\r\\n-style:normal}@font-face{font-family:ProximaNova;src:url(https://cdn.auth0.=\\r\\ncom/fonts/proxima-nova/proximanova-semibold-webfont-webfont.=\\r\\neot);src:url(https://cdn.auth0.com/fonts/proxima-nova/proximanova-semibold-=\\r\\nwebfont-webfont.eot?#iefix) format('embedded-opentype'),url(https://cdn.=\\r\\nauth0.com/fonts/proxima-nova/proximanova-semibold-webfont-webfont.woff) =\\r\\nformat('woff');font-weight:600;font-style:normal}@media only screen and =\\r\\n(max-width:480px){#bodyTable,body{width:100%!important}a,blockquote,body,li=\\r\\n,p,table,td{-webkit-text-size-adjust:none!important}body{min-width:100%!=\\r\\nimportant}#bodyTable{max-width:600px!important}#signIn{max-width:280px!=\\r\\nimportant}}\\r\\n</style>\\r\\n  </head>\\r\\n  <body leftmargin=3D\\\"0\\\" marginwidth=3D\\\"0\\\" =\\r\\ntopmargin=3D\\\"0\\\" marginheight=3D\\\"0\\\" offset=3D\\\"0\\\" style=3D\\\"-webkit-text-size-=\\r\\nadjust: 100%;-ms-text-size-adjust: 100%;margin: 0;padding: 0;font-family: =\\r\\n&quot;ProximaNova&quot;, sans-serif;height: 100% !important;\\\"><center>\\r\\n  <table style=3D\\\"width: 600px;-webkit-text-size-adjust: =\\r\\n100%;-ms-text-size-adjust: 100%;mso-table-lspace: 0pt;mso-table-rspace: =\\r\\n0pt;margin: 0;padding: 0;font-family: &quot;ProximaNova&quot;, =\\r\\nsans-serif;border-collapse: collapse !important;height: 100% !important;\\\" =\\r\\nalign=3D\\\"center\\\" border=3D\\\"0\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" =\\r\\nheight=3D\\\"100%\\\" width=3D\\\"100%\\\" id=3D\\\"bodyTable\\\">\\r\\n    <tr>\\r\\n      <td align=3D\\\"center\\\" valign=3D\\\"top\\\" id=3D\\\"bodyCell\\\" =\\r\\nstyle=3D\\\"-webkit-text-size-adjust: 100%;-ms-text-size-adjust: =\\r\\n100%;mso-table-lspace: 0pt;mso-table-rspace: 0pt;margin: 0;padding: =\\r\\n20px;font-family: &quot;ProximaNova&quot;, sans-serif;height: 100% !=\\r\\nimportant;\\\">\\r\\n      <div class=3D\\\"main\\\">\\r\\n        <p style=3D\\\"text-align: =\\r\\ncenter;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%; =\\r\\nmargin-bottom: 30px;\\\">\\r\\n          <img src=3D\\\"https://www.augmentcode.=\\r\\ncom/android-chrome-512x512.png\\\" width=3D\\\"50\\\" alt=3D\\\"Your logo goes here\\\" =\\r\\nstyle=3D\\\"-ms-interpolation-mode: bicubic;border: 0;height: =\\r\\nauto;line-height: 100%;outline: none;text-decoration: none;\\\">\\r\\n        </p>\\r\\n\\r\\n        <!-- Email change content -->\\r\\n       =20\\r\\n\\r\\n          <!-- Signup =\\r\\nemail content -->\\r\\n         =20\\r\\n\\r\\n            <p style=3D\\\"font-size: 1.4em; =\\r\\nline-height: 1.3;\\\">Your verification code is: <b>762843</b></p>\\r\\n\\r\\n         =20\\r\\n\\r\\n       =20\\r\\n\\r\\n        <p style=3D\\\"-webkit-text-size-adjust: =\\r\\n100%;-ms-text-size-adjust: 100%;\\\">If you are having any issues with your =\\r\\naccount, please don't hesitate to contact us by replying to this mail.</p>\\r\\n\\r\\n        <br>\\r\\n        Thanks!\\r\\n        <br>\\r\\n\\r\\n        <strong>Augment =\\r\\nCode</strong>\\r\\n\\r\\n        <br><br>\\r\\n        <hr style=3D\\\"border: 2px solid =\\r\\n#EAEEF3; border-bottom: 0; margin: 20px 0;\\\">\\r\\n        <p =\\r\\nstyle=3D\\\"text-align: center;color: #A9B3BC;-webkit-text-size-adjust: =\\r\\n100%;-ms-text-size-adjust: 100%;\\\">\\r\\n          If you did not make this =\\r\\nrequest, you can safely ignore this email. Never share this one-time code =\\r\\nwith anyone - Augment support will never ask for your verification code. =\\r\\nYour account remains secure and no action is needed.\\r\\n        </p>\\r\\n      </div>\\r\\n      </td>\\r\\n    </tr>\\r\\n  </table>\\r\\n</center>\\r\\n</body>\\r\\n</html>\\r\\n----_NmP-3446fba16f178292-Part_1--\\r\\n";
        
        // 使用反射调用私有方法
        Method extractMethod = EmailVerificationApiService.class.getDeclaredMethod("extractVerificationCode", String.class);
        extractMethod.setAccessible(true);
        
        String result = (String) extractMethod.invoke(service, emailContent);
        
        assertNotNull("应该能够提取到验证码", result);
        assertEquals("验证码应该是762843", "762843", result);
        
        System.out.println("成功从真实邮箱内容中提取验证码: " + result);
    }
    
    /**
     * 测试各种格式的验证码提取
     */
    @Test
    public void testExtractVerificationCodeVariousFormats() throws Exception {
        Method extractMethod = EmailVerificationApiService.class.getDeclaredMethod("extractVerificationCode", String.class);
        extractMethod.setAccessible(true);
        
        // 测试用例1：标准格式
        String email1 = "Your verification code is: 123456";
        String result1 = (String) extractMethod.invoke(service, email1);
        assertEquals("标准格式应该提取成功", "123456", result1);
        
        // 测试用例2：带HTML标签
        String email2 = "Your verification code is: <b>789012</b>";
        String result2 = (String) extractMethod.invoke(service, email2);
        assertEquals("HTML格式应该提取成功", "789012", result2);
        
        // 测试用例3：多个空格
        String email3 = "Your verification code is:    345678   ";
        String result3 = (String) extractMethod.invoke(service, email3);
        assertEquals("多空格格式应该提取成功", "345678", result3);
        
        // 测试用例4：不同大小写
        String email4 = "your verification CODE IS: 567890";
        String result4 = (String) extractMethod.invoke(service, email4);
        assertEquals("大小写混合应该提取成功", "567890", result4);
        
        // 测试用例5：宽松匹配
        String email5 = "The code is: 234567 for verification";
        String result5 = (String) extractMethod.invoke(service, email5);
        assertEquals("宽松匹配应该提取成功", "234567", result5);
        
        System.out.println("所有格式测试通过");
    }

    /**
     * 测试边界情况和错误处理
     */
    @Test
    public void testExtractVerificationCodeEdgeCases() throws Exception {
        Method extractMethod = EmailVerificationApiService.class.getDeclaredMethod("extractVerificationCode", String.class);
        extractMethod.setAccessible(true);

        // 测试用例1：空字符串
        String result1 = (String) extractMethod.invoke(service, "");
        assertNull("空字符串应该返回null", result1);

        // 测试用例2：null
        String result2 = (String) extractMethod.invoke(service, (String) null);
        assertNull("null应该返回null", result2);

        // 测试用例3：只有空格
        String result3 = (String) extractMethod.invoke(service, "   ");
        assertNull("只有空格应该返回null", result3);

        // 测试用例4：没有验证码
        String email4 = "This is a normal email without any code";
        String result4 = (String) extractMethod.invoke(service, email4);
        assertNull("没有验证码的邮件应该返回null", result4);

        // 测试用例5：验证码位数不对（5位）
        String email5 = "Your verification code is: 12345";
        String result5 = (String) extractMethod.invoke(service, email5);
        assertNull("5位数字应该返回null", result5);

        // 测试用例6：验证码位数不对（7位）
        String email6 = "Your verification code is: 1234567";
        String result6 = (String) extractMethod.invoke(service, email6);
        assertNull("7位数字应该返回null", result6);

        // 测试用例7：包含字母的代码
        String email7 = "Your verification code is: 12A456";
        String result7 = (String) extractMethod.invoke(service, email7);
        assertNull("包含字母的代码应该返回null", result7);

        System.out.println("边界情况测试通过");
    }

    /**
     * 测试parseCodeResponse2方法的完整流程
     */
    @Test
    public void testParseCodeResponse2Success() throws Exception {
        // 构造成功的JSON响应
        JSONObject response = new JSONObject();
        JSONArray results = new JSONArray();
        JSONObject emailObj = new JSONObject();

        // 模拟包含验证码的邮箱内容
        String emailContent = "Welcome to our service! Your verification code is: 123456. Please use this code to verify your account.";
        emailObj.put("row", emailContent);
        results.add(emailObj);
        response.put("results", results);

        // 使用反射调用私有方法
        Method parseMethod = EmailVerificationApiService.class.getDeclaredMethod("parseCodeResponse2", String.class);
        parseMethod.setAccessible(true);

        ApiResponse<String> result = (ApiResponse<String>) parseMethod.invoke(service, response.toString());

        assertTrue("解析应该成功", result.isSuccess());
        assertEquals("验证码应该是123456", "123456", result.getData());
        assertEquals("消息应该正确", "验证码提取成功", result.getMessage());

        System.out.println("parseCodeResponse2成功测试通过: " + result.getData());
    }

    /**
     * 测试parseCodeResponse2方法的失败情况
     */
    @Test
    public void testParseCodeResponse2Failures() throws Exception {
        Method parseMethod = EmailVerificationApiService.class.getDeclaredMethod("parseCodeResponse2", String.class);
        parseMethod.setAccessible(true);

        // 测试用例1：空的results数组
        JSONObject response1 = new JSONObject();
        response1.put("results", new JSONArray());

        ApiResponse<String> result1 = (ApiResponse<String>) parseMethod.invoke(service, response1.toString());
        assertFalse("空results应该失败", result1.isSuccess());
        assertTrue("错误消息应该包含'未找到邮件'", result1.getMessage().contains("未找到邮件"));

        // 测试用例2：邮箱内容为空
        JSONObject response2 = new JSONObject();
        JSONArray results2 = new JSONArray();
        JSONObject emailObj2 = new JSONObject();
        emailObj2.put("row", "");
        results2.add(emailObj2);
        response2.put("results", results2);

        ApiResponse<String> result2 = (ApiResponse<String>) parseMethod.invoke(service, response2.toString());
        assertFalse("空邮箱内容应该失败", result2.isSuccess());
        assertTrue("错误消息应该包含'邮箱内容为空'", result2.getMessage().contains("邮箱内容为空"));

        // 测试用例3：邮箱内容没有验证码
        JSONObject response3 = new JSONObject();
        JSONArray results3 = new JSONArray();
        JSONObject emailObj3 = new JSONObject();
        emailObj3.put("row", "This is a normal email without verification code");
        results3.add(emailObj3);
        response3.put("results", results3);

        ApiResponse<String> result3 = (ApiResponse<String>) parseMethod.invoke(service, response3.toString());
        assertFalse("没有验证码应该失败", result3.isSuccess());
        assertTrue("错误消息应该包含'未找到验证码'", result3.getMessage().contains("未找到验证码"));

        // 测试用例4：无效的JSON
        // ApiResponse<String> result4 = (ApiResponse<String>) parseMethod.invoke(service, "invalid json");
        // assertFalse("无效JSON应该失败", result4.isSuccess());
        // assertTrue("错误消息应该包含'响应格式错误'", result4.getMessage().contains("响应格式错误"));

        System.out.println("parseCodeResponse2失败测试通过");
    }
}
