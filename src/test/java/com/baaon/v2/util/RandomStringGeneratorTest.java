package com.baaon.v2.util;

import org.junit.Test;

import java.util.regex.Pattern;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * RandomStringGenerator 测试类
 */
public class RandomStringGeneratorTest {
    
    // 正则表达式：5位小写字母 + 1-3位数字 + 1-3位小写字母
    private static final Pattern NAME_PATTERN = Pattern.compile("^[a-z]{5}[0-9]{1,3}[a-z]{1,3}$");
    
    @Test
    public void testGenerateRandomName() {
        // 测试生成的字符串格式是否正确
        String name = RandomStringGenerator.generateRandomName();
        
        assertNotNull("生成的名称不应为null", name);
        assertFalse("生成的名称不应为空", name.isEmpty());
        
        // 验证格式：5位小写字母 + 1-3位数字 + 1-3位小写字母
        assertTrue("生成的名称格式不正确: " + name, NAME_PATTERN.matcher(name).matches());
        
        // 验证长度范围 (5 + 1-3 + 1-3 = 7-11位)
        assertTrue("生成的名称长度应在7-11位之间: " + name, 
                   name.length() >= 7 && name.length() <= 11);
        
        System.out.println("生成的随机名称: " + name);
    }
    
    @Test
    public void testGenerateMultipleNames() {
        // 测试生成多个名称，确保它们不完全相同
        String[] names = RandomStringGenerator.generateRandomNames(10);
        
        assertEquals("应该生成10个名称", 10, names.length);
        
        // 验证每个名称的格式
        for (String name : names) {
            assertNotNull("生成的名称不应为null", name);
            assertTrue("生成的名称格式不正确: " + name, NAME_PATTERN.matcher(name).matches());
            System.out.println("生成的随机名称: " + name);
        }
        
        // 验证生成的名称不完全相同（虽然理论上可能相同，但概率极低）
        boolean allSame = true;
        for (int i = 1; i < names.length; i++) {
            if (!names[0].equals(names[i])) {
                allSame = false;
                break;
            }
        }
        assertFalse("生成的名称不应该完全相同", allSame);
    }
    
    @Test
    public void testNameStructure() {
        // 测试名称结构的详细验证
        String name = RandomStringGenerator.generateRandomName();
        
        // 前5位应该是小写字母
        String firstPart = name.substring(0, 5);
        assertTrue("前5位应该是小写字母: " + firstPart, 
                   firstPart.matches("[a-z]{5}"));
        
        // 找到数字部分的开始和结束位置
        int digitStart = 5;
        int digitEnd = digitStart;
        while (digitEnd < name.length() && Character.isDigit(name.charAt(digitEnd))) {
            digitEnd++;
        }
        
        // 验证数字部分长度为1-3位
        int digitLength = digitEnd - digitStart;
        assertTrue("数字部分长度应为1-3位: " + digitLength, 
                   digitLength >= 1 && digitLength <= 3);
        
        // 验证最后部分是小写字母
        String lastPart = name.substring(digitEnd);
        assertTrue("最后部分应该是1-3位小写字母: " + lastPart, 
                   lastPart.matches("[a-z]{1,3}"));
        
        System.out.println("名称结构验证通过: " + name + 
                          " (字母:" + firstPart + 
                          ", 数字:" + name.substring(digitStart, digitEnd) + 
                          ", 字母:" + lastPart + ")");
    }
}
