//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v1;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManager;
import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class SessionIdReplacer {
    private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);

    private boolean a1() {
        try {
            ClassLoader f1 = Thread.currentThread().getContextClassLoader();
            ClassLoader f2 = null;

            try {
                PluginManager pluginManager = PluginManager.getInstance();
                IdeaPluginDescriptor targetPlugin = null;
                IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();

                for(IdeaPluginDescriptor plugin : allPlugins) {
                    if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
                        targetPlugin = plugin;
                        break;
                    }
                }

                if (targetPlugin != null) {
                    f2 = targetPlugin.getPluginClassLoader();
                    LOG.info("成功获取目标插件的类加载器");
                }
            } catch (Exception e) {
                LOG.warn("无法获取目标插件类加载器，将使用当前类加载器: " + e.getMessage());
            }

            if (f2 == null) {
                f2 = this.getClass().getClassLoader();
                LOG.info("使用当前类加载器作为备用方案");
            }

            Thread.currentThread().setContextClassLoader(f2);
            Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, f2);
            Application app = ApplicationManager.getApplication();
            Method method = app.getClass().getMethod("getService", Class.class);
            Object augmentApiInstance = method.invoke(app, apiImplClass);
            Field httpClientField = augmentApiInstance.getClass().getDeclaredField("httpClient");
            httpClientField.setAccessible(true);
            String sessionId = SessionId.INSTANCE.c1();
            LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.c4() + ")");
            Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
            Constructor<?> constructor = httpClientClass.getConstructor(String.class);
            Object newHttpClient = constructor.newInstance(sessionId);
            httpClientField.set(augmentApiInstance, newHttpClient);
            LOG.info("成功重新初始化httpClient实例");
            Thread.currentThread().setContextClassLoader(f1);
            return true;
        } catch (Exception e) {
            LOG.error("重新初始化httpClient实例失败", e);
            return false;
        }
    }

    public boolean a2() {
        try {
            if (this.a1()) {
                LOG.info("SessionId类替换操作成功完成");
                return true;
            } else {
                LOG.warn("所有替换方法都失败，SessionId未能成功替换");
                return false;
            }
        } catch (Exception e) {
            LOG.error("替换SessionId类时出错", e);
            return false;
        }
    }
}
