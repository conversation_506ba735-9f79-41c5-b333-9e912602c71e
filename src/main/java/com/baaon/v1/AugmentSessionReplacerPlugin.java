//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v1;

import com.alibaba.fastjson.JSONObject;
import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class AugmentSessionReplacerPlugin implements AppLifecycleListener {
    private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
    private static volatile boolean isInitialized = false;

    public AugmentSessionReplacerPlugin() {
        this.initialize();
    }

    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        this.initialize();
    }

    public void appStarted() {
        this.initialize();
    }

    private void initialize() {
        if (!isInitialized) {
            try {
                LOG.info("Starting Augment Session ID Replacer Plugin...");
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    try {
                        SessionIdReplacer replacer = new SessionIdReplacer();
                        if (replacer.a2()) {
                            LOG.info("Successfully replaced SessionId class");
                            isInitialized = true;
                        } else {
                            LOG.warn("Failed to replace SessionId class");
                        }
                    } catch (Exception e) {
                        LOG.error("Error during SessionId class replacement", e);
                    }

                });
            } catch (Exception e) {
                LOG.error("Failed to initialize Augment Session ID Replacer", e);
            }
        }

    }

    public static AugmentSessionReplacerPlugin getInstance() {
        return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
    }

    public JSONObject processJsonData(String jsonString) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            jsonObject.put("timestamp", System.currentTimeMillis());
            jsonObject.put("plugin", "augment-assistant");
            jsonObject.put("version", "1.0.0");
            LOG.info("Successfully processed JSON data: " + jsonObject.toString());
            return jsonObject;
        } catch (Exception e) {
            LOG.error("Failed to process JSON data", e);
            return new JSONObject();
        }
    }
}
