//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v1;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class AugmentStartupActivity implements ProjectActivity {
    private static final Logger LOG = Logger.getInstance(AugmentStartupActivity.class);

    public @Nullable Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        LOG.info("开始为项目 [" + project.getName() + "] 替换目标插件类...");

        try {
            SessionIdReplacer replacer = new SessionIdReplacer();
            if (replacer.a2()) {
                LOG.info("项目 [" + project.getName() + "] 的SessionId类替换成功");
            } else {
                LOG.warn("项目 [" + project.getName() + "] 的SessionId类替换失败");
            }
        } catch (Exception e) {
            LOG.error("项目 [" + project.getName() + "] 的SessionId替换过程中发生错误", e);
        }

        return Unit.INSTANCE;
    }
}
