//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v1;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.application.PermanentInstallationID;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.util.UUID;

public class SessionId {
    private static final Logger LOG = Logger.getInstance(SessionId.class);
    public static final @NotNull SessionId INSTANCE = new SessionId();
    private static final @NotNull String SESSION_ID_KEY = "augment.session.id";

    private SessionId() {
    }

    public @NotNull String c1() {
        LOG.info("获取SessionId");
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String storedSessionID = properties.getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return storedSessionID;
        } else {
            String newSessionID = UUID.randomUUID().toString();
            properties.setValue("augment.session.id", newSessionID);
            LOG.info("生成新的SessionId: " + newSessionID);
            return newSessionID;
        }
    }

    public @NotNull String c2() {
        String newSessionId = UUID.randomUUID().toString();
        PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);
        LOG.info("重置SessionId: " + newSessionId);
        return newSessionId;
    }

    private boolean isBlank(String str) {
        return str == null ? true : str.trim().isEmpty();
    }

    public @NotNull String c3() {
        String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return storedSessionID;
        } else {
            String installationID = PermanentInstallationID.get();
            if (installationID != null && !this.isBlank(installationID)) {
                LOG.info("使用PermanentInstallationID作为SessionId: " + installationID);
                return installationID;
            } else {
                LOG.warn("没有找到有效的SessionId或InstallationID");
                return "";
            }
        }
    }

    public boolean hasValidSessionId() {
        String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return true;
        } else {
            String installationID = PermanentInstallationID.get();
            return installationID != null && !this.isBlank(installationID);
        }
    }

    public void clearStoredSessionId() {
        PropertiesComponent.getInstance().unsetValue("augment.session.id");
        LOG.info("已清除存储的SessionId");
    }

    public @NotNull String c4() {
        String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return "PropertiesComponent";
        } else {
            String installationID = PermanentInstallationID.get();
            return installationID != null && !this.isBlank(installationID) ? "PermanentInstallationID" : "Generated";
        }
    }

    public @NotNull String c5() {
        String sessionId = this.c1();
        String source = this.c4();
        return String.format("SessionID: %s (Source: %s)", sessionId, source);
    }
}
