//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v1;

import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Field;
import java.util.Map;

public class UnsafeCrossPluginAccess {
    private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);

    public static Object b1(String pluginId, String serviceClassName) {
        try {
            Application app = ApplicationManager.getApplication();
            Field servicesField = app.getClass().getDeclaredField("myServices");
            servicesField.setAccessible(true);
            Map<?, ?> services = (Map)servicesField.get(app);

            for(Map.Entry<?, ?> entry : services.entrySet()) {
                Object service = entry.getValue();
                String serviceClass = service != null ? service.getClass().getName() : "none";
                LOG.info("发现服务: " + serviceClass);
                if (service != null && service.getClass().getName().equals(serviceClassName)) {
                    LOG.info("找到匹配的服务: " + serviceClassName);
                    return service;
                }
            }

            LOG.warn("未找到指定的服务: " + serviceClassName);
        } catch (Exception e) {
            LOG.error("获取服务时发生异常 - 插件ID: " + pluginId + ", 服务类: " + serviceClassName, e);
        }

        return null;
    }
}
