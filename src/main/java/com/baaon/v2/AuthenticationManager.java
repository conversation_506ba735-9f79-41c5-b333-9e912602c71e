//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.baaon.v2;

import com.baaon.SecuritySHA1Util;
import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class AuthenticationManager {
    private static final Logger LOG = Logger.getInstance(AuthenticationManager.class);
    private static final String FORMAL_VERIFICATION_STATUS_KEY = "augment.formal.verification.status.v2";
    private static final String AUTH_CODE_STORAGE_KEY = "augment.auth.code.storage.v2";
    private static final String EMAIL_ADDRESS_STORAGE_KEY = "augment.email.address.storage.v2";
    private static final String FIXED_TRIAL_CODE = "1024";
    private static final String FORMAL_VERIFICATION_API_URL = "http://*************:82/api/aug/code/active";
    private static final AuthenticationManager INSTANCE = new AuthenticationManager();

    private AuthenticationManager() {
    }

    public static @NotNull AuthenticationManager getInstance() {
        return INSTANCE;
    }

    public boolean verifyTrialCode(@NotNull String trialCode) {
        boolean isValid = "1024".equals(trialCode.trim());
        LOG.info("试用验证码验证结果: " + isValid + " (输入: " + trialCode + ", 期望: 1024)");
        return isValid;
    }

    public boolean activateTrialMode() {
        LOG.info("直接激活试用模式，使用固定验证码: 1024");
        return this.verifyTrialCode("1024");
    }

    public boolean verifyFormalCode(@NotNull String formalCode) throws Exception {
        LOG.info("调用正式验证API: http://*************:82/api/aug/code/active");
        long time = System.currentTimeMillis();
        String encode = SecuritySHA1Util.shaEncode(time + "10f2f96d89a32941edf01bxcz0a076f10");
        URL url = new URL("http://*************:82/api/aug/code/active?ts=" + time + "&sign=" + encode);
        HttpURLConnection connection = (HttpURLConnection)url.openConnection();

        boolean var12;
        try {
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
            String requestBody = this.buildVerificationRequestBody(formalCode);
            LOG.info("正式验证API请求体: " + requestBody);
            OutputStream os = connection.getOutputStream();

            try {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            } catch (Throwable var24) {
                if (os != null) {
                    try {
                        os.close();
                    } catch (Throwable var19) {
                        var24.addSuppressed(var19);
                    }
                }

                throw var24;
            }

            if (os != null) {
                os.close();
            }

            int responseCode = connection.getResponseCode();
            LOG.info("正式验证API响应码: " + responseCode);
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));

                boolean var11;
                try {
                    StringBuilder response = new StringBuilder();

                    String line;
                    while((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    String responseBody = response.toString();
                    LOG.info("正式验证API响应: " + responseBody);
                    boolean isValid = this.parseVerificationResponse(responseBody);
                    LOG.info("正式验证码验证结果: " + isValid);
                    var11 = isValid;
                } catch (Throwable var25) {
                    try {
                        reader.close();
                    } catch (Throwable var18) {
                        var25.addSuppressed(var18);
                    }

                    throw var25;
                }

                reader.close();
                return var11;
            }

            LOG.warn("正式验证API调用失败，响应码: " + responseCode);
            boolean var25 = false;
            var12 = var25;
        } finally {
            connection.disconnect();
        }

        return var12;
    }

    private String buildVerificationRequestBody(@NotNull String code) {
        String deviceId = SessionId.INSTANCE.getSessionId();
        String osName = System.getProperty("os.name", "Unknown");
        String osVersion = System.getProperty("os.version", "Unknown");
        String operatingSystem = osName + " " + osVersion;
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"code\":\"").append(this.escapeJson(code)).append("\",");
        json.append("\"deviceId\":\"").append(this.escapeJson(deviceId)).append("\",");
        json.append("\"deviceType\":\"Desktop\",");
        json.append("\"operatingSystem\":\"").append(this.escapeJson(operatingSystem)).append("\",");
        json.append("\"browserInfo\":\"IntelliJ IDEA Plugin\",");
        json.append("\"userAgent\":\"Augment-Assistant-Plugin\"");
        json.append("}");
        return json.toString();
    }

    private boolean parseVerificationResponse(@NotNull String responseBody) {
        try {
            if (!responseBody.contains("\"success\":true")) {
                LOG.warn("外层验证失败，响应: " + responseBody);
                return false;
            } else {
                int dataIndex = responseBody.indexOf("\"data\":");
                if (dataIndex == -1) {
                    LOG.warn("响应中未找到data字段");
                    return false;
                } else {
                    String dataSection = responseBody.substring(dataIndex);
                    boolean dataSuccess = dataSection.contains("\"success\":true");
                    if (!dataSuccess) {
                        LOG.warn("data.success为false，验证失败");
                        if (dataSection.contains("\"remainingAttempts\"")) {
                            LOG.info("响应包含剩余尝试次数信息");
                        }
                    }

                    return dataSuccess;
                }
            }
        } catch (Exception e) {
            LOG.error("解析验证响应失败", e);
            return false;
        }
    }

    private String escapeJson(@NotNull String str) {
        return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r").replace("\t", "\\t");
    }

    public void saveFormalVerificationStatus(boolean verified) {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.setValue("augment.formal.verification.status.v2", verified);
        LOG.info("保存正式验证状态: " + verified);
        if (verified) {
            TrialSessionManager.getInstance().clearTrialData();
            LOG.info("正式验证成功，已清理试用数据");
        }

    }

    public boolean isFormallyVerified() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getBoolean("augment.formal.verification.status.v2", false);
    }

    public void clearFormalVerificationStatus() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue("augment.formal.verification.status.v2");
        LOG.info("已清除正式验证状态");
    }

    public @NotNull String getAuthenticationStatus() {
        if (this.isFormallyVerified()) {
            return "正式认证";
        } else if (TrialSessionManager.getInstance().hasValidTrialSession()) {
            int remainingDays = TrialSessionManager.getInstance().getRemainingDays();
            return "试用认证 (剩余 " + remainingDays + " 天)";
        } else {
            return "未认证";
        }
    }

    public boolean hasValidAuthentication() {
        return this.isFormallyVerified() || TrialSessionManager.getInstance().hasValidTrialSession();
    }

    public void saveAuthCode(@NotNull String authCode) {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.setValue("augment.auth.code.storage.v2", authCode.trim());
        LOG.info("保存授权码: " + authCode.trim());
    }

    public String getSavedAuthCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String authCode = properties.getValue("augment.auth.code.storage.v2");
        if (authCode != null && !authCode.trim().isEmpty()) {
            LOG.info("获取保存的授权码: " + authCode);
            return authCode.trim();
        } else {
            LOG.info("未找到保存的授权码");
            return null;
        }
    }

    public void saveEmailJwt(@NotNull String jwt) {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.setValue("augment.auth.jwt.storage.v2", jwt.trim());
        LOG.info("保存授权码: " + jwt.trim());
    }

    public String getEmailJwt() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String authCode = properties.getValue("augment.auth.jwt.storage.v2");
        if (authCode != null && !authCode.trim().isEmpty()) {
            LOG.info("获取保存的jwt: " + authCode);
            return authCode.trim();
        } else {
            LOG.info("未找到保存的jwt");
            return null;
        }
    }

    public void clearSavedAuthCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue("augment.auth.code.storage.v2");
        LOG.info("已清除保存的授权码");
    }

    public boolean hasSavedAuthCode() {
        String authCode = this.getSavedAuthCode();
        return authCode != null && !authCode.trim().isEmpty();
    }

    public void saveEmailAddress(@NotNull String emailAddress) {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.setValue("augment.email.address.storage.v2", emailAddress.trim());
        LOG.info("保存邮箱地址: " + emailAddress.trim());
    }

    public String getSavedEmailAddress() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String emailAddress = properties.getValue("augment.email.address.storage.v2");
        if (emailAddress != null && !emailAddress.trim().isEmpty()) {
            LOG.info("获取保存的邮箱地址: " + emailAddress);
            return emailAddress.trim();
        } else {
            LOG.info("未找到保存的邮箱地址");
            return null;
        }
    }

    public void clearSavedEmailAddress() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue("augment.email.address.storage.v2");
        LOG.info("已清除保存的邮箱地址");
    }

    public boolean hasSavedEmailAddress() {
        String emailAddress = this.getSavedEmailAddress();
        return emailAddress != null && !emailAddress.trim().isEmpty();
    }

    public void clearAllSavedData() {
        this.clearFormalVerificationStatus();
        this.clearSavedAuthCode();
        this.clearSavedEmailAddress();
        LOG.info("已清除所有保存的数据");
    }
}
