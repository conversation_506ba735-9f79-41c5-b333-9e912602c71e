//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

public class TrialSessionManager {
    private static final Logger LOG = Logger.getInstance(TrialSessionManager.class);
    private static final String TRIAL_CODE_KEY = "augment.trial.code";
    private static final String TRIAL_START_TIME_KEY = "augment.trial.start.time";
    private static final String TRIAL_SESSION_ID_KEY = "augment.trial.session.id";
    private static final String ORIGINAL_SESSION_ID_KEY = "augment.original.session.id";
    private static final int TRIAL_DAYS = 3;
    private static final TrialSessionManager INSTANCE = new TrialSessionManager();

    private TrialSessionManager() {
    }

    public static @NotNull TrialSessionManager getInstance() {
        return INSTANCE;
    }

    public boolean activateTrialCode(String trialCode) {
        if (trialCode == null) {
            return false;
        } else if (trialCode.trim().isEmpty()) {
            return false;
        } else {
            PropertiesComponent properties = PropertiesComponent.getInstance();
            String existingCode = properties.getValue("augment.trial.code");
            if (existingCode != null && !existingCode.trim().isEmpty()) {
                LOG.info("试用验证码已存在，无需重复激活");
                return true;
            } else {
                String currentSessionId = SessionId.INSTANCE.getSessionId();
                properties.setValue("augment.original.session.id", currentSessionId);
                String trialSessionId = UUID.randomUUID().toString();
                properties.setValue("augment.trial.session.id", trialSessionId);
                properties.setValue("augment.trial.code", trialCode);
                properties.setValue("augment.trial.start.time", String.valueOf(Instant.now().toEpochMilli()));
                LOG.info("试用验证码激活成功: " + trialCode + ", 试用SessionId: " + trialSessionId);
                return true;
            }
        }
    }

    public boolean hasValidTrialSession() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String trialCode = properties.getValue("augment.trial.code");
        String startTimeStr = properties.getValue("augment.trial.start.time");
        if (trialCode != null && !trialCode.trim().isEmpty() && startTimeStr != null) {
            try {
                long startTime = Long.parseLong(startTimeStr);
                Instant startInstant = Instant.ofEpochMilli(startTime);
                Instant now = Instant.now();
                long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
                boolean isValid = daysPassed < 3L;
                if (!isValid) {
                    LOG.info("试用期已过期，已过去 " + daysPassed + " 天");
                    this.clearTrialData();
                }

                return isValid;
            } catch (NumberFormatException e) {
                LOG.error("解析试用开始时间失败", e);
                return false;
            }
        } else {
            return false;
        }
    }

    public @Nullable String getTrialSessionId() {
        if (!this.hasValidTrialSession()) {
            return null;
        } else {
            PropertiesComponent properties = PropertiesComponent.getInstance();
            return properties.getValue("augment.trial.session.id");
        }
    }

    public @Nullable String getOriginalSessionId() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getValue("augment.original.session.id");
    }

    public @Nullable String getTrialCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        return properties.getValue("augment.trial.code");
    }

    public int getRemainingDays() {
        if (!this.hasValidTrialSession()) {
            return 0;
        } else {
            PropertiesComponent properties = PropertiesComponent.getInstance();
            String startTimeStr = properties.getValue("augment.trial.start.time");
            if (startTimeStr == null) {
                return 0;
            } else {
                try {
                    long startTime = Long.parseLong(startTimeStr);
                    Instant startInstant = Instant.ofEpochMilli(startTime);
                    Instant now = Instant.now();
                    long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
                    return Math.max(0, 3 - (int)daysPassed);
                } catch (NumberFormatException e) {
                    LOG.error("解析试用开始时间失败", e);
                    return 0;
                }
            }
        }
    }

    public void clearTrialData() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        properties.unsetValue("augment.trial.code");
        properties.unsetValue("augment.trial.start.time");
        properties.unsetValue("augment.trial.session.id");
        properties.unsetValue("augment.original.session.id");
        LOG.info("试用数据已清理");
    }

    public boolean hasTrialCode() {
        PropertiesComponent properties = PropertiesComponent.getInstance();
        String trialCode = properties.getValue("augment.trial.code");
        return trialCode != null && !trialCode.trim().isEmpty();
    }
}
