//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.application.PermanentInstallationID;
import com.intellij.openapi.diagnostic.Logger;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;

public class SessionId {
    private static final Logger LOG = Logger.getInstance(SessionId.class);
    public static final @NotNull SessionId INSTANCE = new SessionId();
    private static final @NotNull String SESSION_ID_KEY = "augment.session.id";

    private SessionId() {
    }

    public @NotNull String getSessionId() {
        LOG.info("获取SessionId");
        TrialSessionManager trialManager = TrialSessionManager.getInstance();
        if (trialManager.hasValidTrialSession()) {
            String trialSessionId = trialManager.getTrialSessionId();
            if (trialSessionId != null && !this.isBlank(trialSessionId)) {
                LOG.info("使用试用SessionId: " + trialSessionId);
                return trialSessionId;
            }
        }

        PropertiesComponent properties = PropertiesComponent.getInstance();
        String storedSessionID = properties.getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return storedSessionID;
        } else {
            String installationID = PermanentInstallationID.get();
            if (!this.isBlank(installationID)) {
                return installationID;
            } else {
                String newSessionID = UUID.randomUUID().toString();
                properties.setValue("augment.session.id", newSessionID);
                return newSessionID;
            }
        }
    }

    public @NotNull String resetSessionId() {
        String newSessionId = UUID.randomUUID().toString();
        PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);
        return newSessionId;
    }

    private boolean isBlank(@NotNull String str) {
        return str.trim().isEmpty();
    }

    public @NotNull String getStoredSessionId() {
        String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return storedSessionID;
        } else {
            String installationID = PermanentInstallationID.get();
            return installationID != null && !this.isBlank(installationID) ? installationID : "";
        }
    }

    public boolean hasValidSessionId() {
        String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
        if (storedSessionID != null && !this.isBlank(storedSessionID)) {
            return true;
        } else {
            String installationID = PermanentInstallationID.get();
            return installationID != null && !this.isBlank(installationID);
        }
    }

    public void clearStoredSessionId() {
        PropertiesComponent.getInstance().unsetValue("augment.session.id");
    }

    public @NotNull String getSessionIdSource() {
        TrialSessionManager trialManager = TrialSessionManager.getInstance();
        if (trialManager.hasValidTrialSession()) {
            return "TrialSession";
        } else {
            String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
            if (storedSessionID != null && !this.isBlank(storedSessionID)) {
                return "PropertiesComponent";
            } else {
                String installationID = PermanentInstallationID.get();
                return !this.isBlank(installationID) ? "PermanentInstallationID" : "Generated";
            }
        }
    }

    public @NotNull String getSessionIdInfo() {
        String sessionId = this.getSessionId();
        String source = this.getSessionIdSource();
        return String.format("SessionID: %s (Source: %s)", sessionId, source);
    }
}
