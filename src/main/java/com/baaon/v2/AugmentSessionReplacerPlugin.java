//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class AugmentSessionReplacerPlugin implements AppLifecycleListener {
    private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
    private static volatile boolean isInitialized = false;

    public AugmentSessionReplacerPlugin() {
        this.initialize();
    }

    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        this.initialize();
    }

    public void appStarted() {
        this.initialize();
    }

    private void initialize() {
        if (!isInitialized) {
            try {
                LOG.info("Starting Augment Session ID Replacer Plugin...");
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    try {
                        SessionIdReplacer replacer = new SessionIdReplacer();
                        if (replacer.replaceSessionIdClass()) {
                            LOG.info("Successfully replaced SessionId class");
                            isInitialized = true;
                        } else {
                            LOG.warn("Failed to replace SessionId class");
                        }
                    } catch (Exception e) {
                        LOG.error("Error during SessionId class replacement", e);
                    }

                });
            } catch (Exception e) {
                LOG.error("Failed to initialize Augment Session ID Replacer", e);
            }
        }

    }

    public static AugmentSessionReplacerPlugin getInstance() {
        return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
    }
}
