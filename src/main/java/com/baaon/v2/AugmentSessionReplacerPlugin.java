//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class AugmentSessionReplacerPlugin implements AppLifecycleListener {
    private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
    private static volatile boolean isInitialized = false;
    private static volatile boolean isInitializing = false;
    private static final Object initLock = new Object();
    private static volatile long lastInitAttempt = 0;
    private static final long INIT_RETRY_INTERVAL = 30000; // 30秒重试间隔

    public AugmentSessionReplacerPlugin() {
        this.initialize();
    }

    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        this.initialize();
    }

    public void appStarted() {
        this.initialize();
    }

    private void initialize() {
        // 双重检查锁定模式，确保线程安全
        if (!isInitialized && !isInitializing) {
            synchronized (initLock) {
                if (!isInitialized && !isInitializing) {
                    // 检查是否需要等待重试间隔
                    long currentTime = System.currentTimeMillis();
                    if (lastInitAttempt > 0 && (currentTime - lastInitAttempt) < INIT_RETRY_INTERVAL) {
                        LOG.info("初始化重试间隔未到，跳过本次初始化尝试");
                        return;
                    }

                    isInitializing = true;
                    lastInitAttempt = currentTime;

                    try {
                        LOG.info("Starting Augment Session ID Replacer Plugin...");
                        ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            try {
                                SessionIdReplacer replacer = new SessionIdReplacer();
                                if (replacer.replaceSessionIdClass()) {
                                    LOG.info("Successfully replaced SessionId class");
                                    synchronized (initLock) {
                                        isInitialized = true;
                                        isInitializing = false;
                                    }
                                } else {
                                    LOG.warn("Failed to replace SessionId class");
                                    synchronized (initLock) {
                                        isInitializing = false;
                                    }
                                }
                            } catch (Exception e) {
                                LOG.error("Error during SessionId class replacement", e);
                                synchronized (initLock) {
                                    isInitializing = false;
                                }
                            }
                        });
                    } catch (Exception e) {
                        LOG.error("Failed to initialize Augment Session ID Replacer", e);
                        synchronized (initLock) {
                            isInitializing = false;
                        }
                    }
                }
            }
        } else if (isInitialized) {
            LOG.debug("Augment Session ID Replacer already initialized, skipping");
        } else if (isInitializing) {
            LOG.debug("Augment Session ID Replacer initialization in progress, skipping");
        }
    }

    public static AugmentSessionReplacerPlugin getInstance() {
        return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
    }
}
