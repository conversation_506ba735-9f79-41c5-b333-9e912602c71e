package com.baaon.v2.util;

import java.util.Random;

/**
 * 随机字符串生成器
 * 生成规则：5位小写字母 + 1-3位数字 + 1-3位小写字母
 */
public class RandomStringGenerator {
    
    private static final String LOWERCASE_LETTERS = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final Random random = new Random();
    
    /**
     * 生成随机字符串
     * 格式：5位小写字母 + 1-3位数字 + 1-3位小写字母
     * 
     * @return 生成的随机字符串
     */
    public static String generateRandomName() {
        StringBuilder sb = new StringBuilder();
        
        // 生成5位小写字母
        for (int i = 0; i < 5; i++) {
            sb.append(LOWERCASE_LETTERS.charAt(random.nextInt(LOWERCASE_LETTERS.length())));
        }
        
        // 生成1-3位数字
        int digitCount = random.nextInt(3) + 1; // 1到3之间的随机数
        for (int i = 0; i < digitCount; i++) {
            sb.append(DIGITS.charAt(random.nextInt(DIGITS.length())));
        }
        
        // 生成1-3位小写字母
        int letterCount = random.nextInt(3) + 1; // 1到3之间的随机数
        for (int i = 0; i < letterCount; i++) {
            sb.append(LOWERCASE_LETTERS.charAt(random.nextInt(LOWERCASE_LETTERS.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 生成指定数量的随机字符串
     * 
     * @param count 生成数量
     * @return 随机字符串数组
     */
    public static String[] generateRandomNames(int count) {
        String[] names = new String[count];
        for (int i = 0; i < count; i++) {
            names[i] = generateRandomName();
        }
        return names;
    }
}
