//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baaon.SecuritySHA1Util;
import com.baaon.v2.AuthenticationManager;
import com.baaon.v2.util.RandomStringGenerator;
import com.intellij.openapi.diagnostic.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class EmailVerificationApiService {
    private static final Logger LOG = Logger.getInstance(EmailVerificationApiService.class);
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(EmailVerificationApiService.class);

    private AuthenticationManager authManager = AuthenticationManager.getInstance();

    public ApiResponse<String> generateEmail(String authCode) {
        if (authCode != null && !authCode.trim().isEmpty()) {
            try {
                long time = System.currentTimeMillis();
                String encode = SecuritySHA1Util.shaEncode(time + "10f2f96d89a32941edf01bxcz0a076f10");
                String apiUrl = "http://60.204.224.73:82/api/aug/email/generate?ts=" + time + "&sign=" + encode + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
                LOG.info("调用生成邮箱API: " + apiUrl);
                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection)url.openConnection();

                ApiResponse var11;
                try {
                    connection.setRequestMethod("POST");
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(10000);
                    connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
                    connection.setRequestProperty("Accept", "application/json");
                    int responseCode = connection.getResponseCode();
                    LOG.info("生成邮箱API响应码: " + responseCode);
                    if (responseCode == 200) {
                        String responseBody = this.readResponse(connection);
                        LOG.info("生成邮箱API响应: " + responseBody);
                        ApiResponse var7 = this.parseEmailResponse(responseBody);
                        return var7;
                    }

                    LOG.warn("生成邮箱API调用失败，响应码: " + responseCode);
                    ApiResponse var13 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
                    var11 = var13;
                } finally {
                    connection.disconnect();
                }

                return var11;
            } catch (Exception e) {
                LOG.error("生成邮箱API调用异常", e);
                return ApiResponse.failure("网络请求异常: " + e.getMessage());
            }
        } else {
            return ApiResponse.failure("授权码不能为空");
        }
    }

    public ApiResponse<String> generateEmail2(String authCode) {
        if (authCode != null && !authCode.trim().isEmpty()) {
            try {
                String name = RandomStringGenerator.generateRandomName();
                String apiUrl = "https://apimail.deepstudy.fun/admin/new_address";
                LOG.info("调用生成邮箱API: " + apiUrl);

                // 构建JSON请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("enablePrefix", true);
                requestBody.put("name", name);
                requestBody.put("domain", "deepstudy.fun");

                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection)url.openConnection();

                ApiResponse var11;
                try {
                    connection.setRequestMethod("POST");
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(10000);
                    connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
                    connection.setRequestProperty("Accept", "application/json");
                    connection.setRequestProperty("x-admin-auth", "@cloudmail");
                    connection.setRequestProperty("Content-Type", "application/json");
                    connection.setDoOutput(true);

                    // 发送JSON请求体
                    try (java.io.OutputStream os = connection.getOutputStream()) {
                        byte[] input = requestBody.toString().getBytes(StandardCharsets.UTF_8);
                        os.write(input, 0, input.length);
                    }
                    int responseCode = connection.getResponseCode();
                    LOG.info("生成邮箱API响应码: " + responseCode);
                    if (responseCode == 200) {
                        String responseBody = this.readResponse(connection);
                        LOG.info("生成邮箱API响应: " + responseBody);
                        ApiResponse var7 = this.parseEmailResponse2(responseBody);
                        return var7;
                    }

                    LOG.warn("生成邮箱API调用失败，响应码: " + responseCode);
                    ApiResponse var13 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
                    var11 = var13;
                } finally {
                    connection.disconnect();
                }

                return var11;
            } catch (Exception e) {
                LOG.error("生成邮箱API调用异常", e);
                return ApiResponse.failure("网络请求异常: " + e.getMessage());
            }
        } else {
            return ApiResponse.failure("授权码不能为空");
        }
    }

    public ApiResponse<String> queryVerificationCode(String email, String authCode) {
        if (email != null && !email.trim().isEmpty()) {
            if (authCode != null && !authCode.trim().isEmpty()) {
                try {
                    String var10000 = URLEncoder.encode(email, StandardCharsets.UTF_8.toString());
                    long time = System.currentTimeMillis();
                    String encode = SecuritySHA1Util.shaEncode(time + "10f2f96d89a32941edf01bxcz0a076f10");
                    String apiUrl = "http://60.204.224.73:82/api/aug/email/code/latest?ts=" + time + "&sign=" + encode + "&email=" + var10000 + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
                    LOG.info("调用查询验证码API: " + apiUrl);
                    URL url = new URL(apiUrl);
                    HttpURLConnection connection = (HttpURLConnection)url.openConnection();

                    ApiResponse var13;
                    try {
                        connection.setRequestMethod("POST");
                        connection.setConnectTimeout(10000);
                        connection.setReadTimeout(10000);
                        connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
                        connection.setRequestProperty("Accept", "application/json");
                        int responseCode = connection.getResponseCode();
                        LOG.info("查询验证码API响应码: " + responseCode);
                        if (responseCode == 200) {
                            String responseBody = this.readResponse(connection);
                            LOG.info("查询验证码API响应: " + responseBody);
                            ApiResponse var8 = this.parseCodeResponse(responseBody);
                            return var8;
                        }

                        LOG.warn("查询验证码API调用失败，响应码: " + responseCode);
                        ApiResponse var14 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
                        var13 = var14;
                    } finally {
                        connection.disconnect();
                    }

                    return var13;
                } catch (Exception e) {
                    LOG.error("查询验证码API调用异常", e);
                    return ApiResponse.failure("网络请求异常: " + e.getMessage());
                }
            } else {
                return ApiResponse.failure("授权码不能为空");
            }
        } else {
            return ApiResponse.failure("邮箱地址不能为空");
        }
    }

    public ApiResponse<String> queryVerificationCode2(String email, String authCode) {
        if (email != null && !email.trim().isEmpty()) {
            if (authCode != null && !authCode.trim().isEmpty()) {
                try {
                    String apiUrl = "https://apimail.deepstudy.fun/api/mails?limit=10&offset=0";
                    LOG.info("调用查询验证码API: " + apiUrl);
                    URL url = new URL(apiUrl);
                    HttpURLConnection connection = (HttpURLConnection)url.openConnection();
                    String jwt = this.authManager.getEmailJwt();
                    ApiResponse var13;
                    try {
                        connection.setRequestMethod("GET");
                        connection.setConnectTimeout(10000);
                        connection.setReadTimeout(10000);
                        // connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
                        // connection.setRequestProperty("Accept", "application/json");
                        connection.setRequestProperty("Authorization", "Bearer " + jwt);
                        connection.setRequestProperty("Content-Type", "application/json");
                        int responseCode = connection.getResponseCode();
                        LOG.info("查询验证码API响应码: " + responseCode);
                        if (responseCode == 200) {
                            String responseBody = this.readResponse(connection);
                            LOG.info("查询验证码API响应: " + responseBody);
                            ApiResponse var8 = this.parseCodeResponse2(responseBody);
                            return var8;
                        }

                        LOG.warn("查询验证码API调用失败，响应码: " + responseCode);
                        ApiResponse var14 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
                        var13 = var14;
                    } finally {
                        connection.disconnect();
                    }

                    return var13;
                } catch (Exception e) {
                    LOG.error("查询验证码API调用异常", e);
                    return ApiResponse.failure("网络请求异常: " + e.getMessage());
                }
            } else {
                return ApiResponse.failure("授权码不能为空");
            }
        } else {
            return ApiResponse.failure("邮箱地址不能为空");
        }
    }

    private String readResponse(HttpURLConnection connection) throws Exception {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();

            String line;
            while((line = reader.readLine()) != null) {
                response.append(line);
            }

            return response.toString();
        }
    }

    private ApiResponse<String> parseEmailResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            if ((boolean) jsonResponse.getOrDefault("success", false)) {
                String data = jsonResponse.getString("data");
                if (data != null && !data.trim().isEmpty()) {
                    return ApiResponse.success(data, "邮箱生成成功");
                }
            }

            String message = jsonResponse.getOrDefault("message", "邮箱生成失败").toString();
            return ApiResponse.failure(message);
        } catch (Exception e) {
            LOG.error("解析邮箱响应JSON失败", e);
            return ApiResponse.failure("响应格式错误");
        }
    }

    private ApiResponse<String> parseEmailResponse2(String responseBody) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            String address = jsonResponse.getString("address");
            String jwt = jsonResponse.getString("jwt");
            this.authManager.saveEmailJwt(jwt);
            return ApiResponse.success(address, "邮箱生成成功");
        } catch (Exception e) {
            LOG.error("解析邮箱响应JSON失败", e);
            return ApiResponse.failure("响应格式错误");
        }
    }

    private ApiResponse<String> parseCodeResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            if ((boolean) jsonResponse.getOrDefault("success", false)) {
                String data = jsonResponse.getString("data");
                if (data != null && !data.trim().isEmpty()) {
                    return ApiResponse.success(data, "验证码查询成功");
                } else {
                    data = jsonResponse.getOrDefault("message", "验证码查询失败").toString();
                    return ApiResponse.failure(data);
                }
            } else {
                LOG.warn("验证码查询失败，响应: " + responseBody);
                return ApiResponse.failure("验证码查询失败:" + jsonResponse.getString("message"));
            }
        } catch (Exception e) {
            LOG.error("解析验证码响应JSON失败", e);
            return ApiResponse.failure("响应格式错误");
        }
    }

    private ApiResponse<String> parseCodeResponse2(String responseBody) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            JSONArray results = jsonResponse.getJSONArray("results");
            if (results != null && results.size() > 0) {
                JSONObject resObj = results.getJSONObject(0);
                String raw = resObj.getString("raw");
                log.info(raw);

                if (raw != null && !raw.trim().isEmpty()) {
                    // 从邮箱内容中提取验证码
                    String verificationCode = extractVerificationCode(raw);
                    if (verificationCode != null) {
                        LOG.info("成功提取验证码: " + verificationCode);
                        return ApiResponse.success(verificationCode, "验证码提取成功");
                    } else {
                        LOG.warn("未能从邮箱内容中找到验证码");
                        return ApiResponse.failure("未找到验证码");
                    }
                } else {
                    LOG.warn("邮箱内容为空");
                    return ApiResponse.failure("邮箱内容为空");
                }
            } else {
                LOG.warn("验证码查询失败，响应: " + responseBody);
                return ApiResponse.failure("验证码查询失败: 未找到邮件");
            }
        } catch (Exception e) {
            LOG.error("解析验证码响应JSON失败", e);
            return ApiResponse.failure("响应格式错误");
        }
    }

    /**
     * 从邮箱内容中提取验证码
     * @param emailContent 邮箱内容
     * @return 验证码，如果未找到则返回null
     */
    private String extractVerificationCode(String emailContent) {
        if (emailContent == null || emailContent.trim().isEmpty()) {
            return null;
        }

        try {
            // 使用正则表达式匹配 "Your verification code is:" 后面的恰好6位数字
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "Your verification code is:?\\s*(?:<[^>]*>)?\\s*(\\d{6})(?!\\d)",
                java.util.regex.Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher matcher = pattern.matcher(emailContent);

            if (matcher.find()) {
                String code = matcher.group(1);
                LOG.info("找到验证码: " + code);
                return code;
            }

            // 如果上面的模式没有匹配，尝试更宽松的模式
            pattern = java.util.regex.Pattern.compile(
                "code is:?\\s*(?:<[^>]*>)?\\s*(\\d{6})(?!\\d)",
                java.util.regex.Pattern.CASE_INSENSITIVE
            );
            matcher = pattern.matcher(emailContent);

            if (matcher.find()) {
                String code = matcher.group(1);
                LOG.info("找到验证码（宽松匹配）: " + code);
                return code;
            }

            LOG.warn("未能从邮箱内容中匹配到验证码模式");
            return null;
        } catch (Exception e) {
            LOG.error("提取验证码时发生异常", e);
            return null;
        }
    }
}
