//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2.config;

import com.baaon.v2.AuthenticationManager;
import com.baaon.v2.SessionId;
import com.baaon.v2.SessionIdReplacer;
import com.baaon.v2.TrialSessionManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.ui.Messages;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBTextField;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI.Borders;

import javax.swing.*;
import java.awt.*;
import java.awt.Desktop.Action;
import java.awt.datatransfer.ClipboardOwner;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URI;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class AugmentConfigPanel {
    private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
    private JPanel mainPanel;
    private JBTextField sessionIdField;
    private JBLabel sessionIdSourceLabel;
    private JButton trialActivateButton;
    private JBLabel trialStatusLabel;
    private JPanel trialPanel;
    private JBTextField formalCodeField;
    private JButton formalVerifyButton;
    private JButton formalHelpButton;
    private JButton formalTutorialButton;
    private JBLabel formalStatusLabel;
    private JButton generateButton;
    private JButton copyButton;
    private JButton debugClearButton;
    private JBTextField emailField;
    private JButton generateEmailButton;
    private JButton copyEmailButton;
    private JButton queryCodeButton;
    private JButton copyCodeButton;
    private JBLabel emailStatusLabel;
    private JBLabel codeStatusLabel;
    private String originalSessionId;
    private boolean modified = false;
    private AuthenticationManager authManager = AuthenticationManager.getInstance();
    private TrialSessionManager trialManager = TrialSessionManager.getInstance();
    private EmailVerificationApiService apiService = new EmailVerificationApiService();
    private ScheduledExecutorService statusUpdateScheduler;
    private volatile boolean isDisposed = false;
    private long lastQueryTime = 0L;
    private static final int QUERY_INTERVAL_SECONDS = 5;
    private Timer queryTimer;
    private Timer intervalTimer;

    public AugmentConfigPanel() {
        this.initializeComponents();
        this.setupLayout();
        this.setupEventHandlers();
        this.loadAuthenticationStatus();
        this.loadCurrentSettings();
        this.startStatusUpdateScheduler();
    }

    private void initializeComponents() {
        this.trialActivateButton = new JButton("激活3天试用");
        this.trialActivateButton.setToolTipText("点击激活3天试用期（验证码：1024）");
        this.trialStatusLabel = new JBLabel("点击按钮激活3天试用期");
        this.trialStatusLabel.setFont(this.trialStatusLabel.getFont().deriveFont(2));
        this.trialStatusLabel.setForeground(Color.ORANGE);
        this.formalCodeField = new JBTextField();
        this.formalCodeField.setColumns(15);
        this.formalCodeField.setToolTipText("输入正式验证码，永久解锁功能");
        this.formalVerifyButton = new JButton("正式验证");
        this.formalHelpButton = new JButton("如何获取正式验证码？");
        this.formalHelpButton.setBackground(new Color(76, 175, 80));
        this.formalHelpButton.setForeground(Color.WHITE);
        this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
        this.formalTutorialButton = new JButton("使用教程");
        this.formalTutorialButton.setBackground(new Color(255, 193, 7));
        this.formalTutorialButton.setForeground(Color.BLACK);
        this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
        this.formalStatusLabel = new JBLabel("输入正式验证码永久解锁功能");
        this.formalStatusLabel.setFont(this.formalStatusLabel.getFont().deriveFont(2));
        this.formalStatusLabel.setForeground(Color.ORANGE);
        this.sessionIdField = new JBTextField();
        this.sessionIdField.setEditable(false);
        this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
        this.sessionIdSourceLabel = new JBLabel();
        this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
        this.generateButton = new JButton("生成新的SessionId");
        this.copyButton = new JButton("复制到剪贴板");
        this.emailField = new JBTextField();
        this.emailField.setColumns(25);
        this.emailField.setEditable(false);
        this.emailField.setFont(new Font("Monospaced", 0, 12));
        this.emailField.setToolTipText("自动生成的邮箱地址，用于接收验证码");
        this.generateEmailButton = new JButton("生成邮箱");
        this.generateEmailButton.setToolTipText("点击生成一个临时邮箱地址");
        this.copyEmailButton = new JButton("复制邮箱");
        this.copyEmailButton.setToolTipText("复制邮箱地址到剪贴板");
        this.copyEmailButton.setEnabled(false);
        this.queryCodeButton = new JButton("查询验证码");
        this.queryCodeButton.setToolTipText("查询邮箱中最新的验证码（需要约30秒时间，点击间隔30秒）");
        this.queryCodeButton.setEnabled(false);
        this.copyCodeButton = new JButton("复制验证码");
        this.copyCodeButton.setToolTipText("复制验证码到剪贴板");
        this.copyCodeButton.setEnabled(false);
        this.emailStatusLabel = new JBLabel("输入正式验证码后可生成邮箱");
        this.emailStatusLabel.setFont(this.emailStatusLabel.getFont().deriveFont(2));
        this.emailStatusLabel.setForeground(Color.ORANGE);
        this.codeStatusLabel = new JBLabel("生成邮箱后可查询验证码");
        this.codeStatusLabel.setFont(this.codeStatusLabel.getFont().deriveFont(2));
        this.codeStatusLabel.setForeground(Color.GRAY);
        this.debugClearButton = new JButton("清除所有认证状态(调试)");
        this.debugClearButton.setToolTipText("清除所有试用和正式验证状态，仅用于调试");
        this.debugClearButton.setForeground(Color.RED);
        this.debugClearButton.setVisible(false);
        this.updateButtonStates();
    }

    private void setupLayout() {
        this.trialPanel = new JPanel(new FlowLayout(0, 5, 0));
        this.trialPanel.add(this.trialActivateButton);
        JPanel formalPanel = new JPanel(new FlowLayout(0, 5, 0));
        formalPanel.add(this.formalCodeField);
        formalPanel.add(this.formalVerifyButton);
        formalPanel.add(this.formalHelpButton);
        formalPanel.add(this.formalTutorialButton);
        JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
        buttonPanel.add(this.generateButton);
        buttonPanel.add(this.copyButton);
        JPanel emailPanel = new JPanel(new FlowLayout(0, 5, 0));
        emailPanel.add(this.generateEmailButton);
        emailPanel.add(this.emailField);
        emailPanel.add(this.copyEmailButton);
        JPanel codePanel = new JPanel(new FlowLayout(0, 5, 0));
        codePanel.add(this.queryCodeButton);
        codePanel.add(this.copyCodeButton);
        JPanel debugPanel = new JPanel(new FlowLayout(0, 5, 0));
        debugPanel.add(this.debugClearButton);
        FormBuilder formBuilder = FormBuilder.createFormBuilder();
        formBuilder.addLabeledComponent("试用激活:", this.trialPanel, 1, false).addComponentToRightColumn(this.trialStatusLabel, 1).addSeparator().addLabeledComponent("正式验证码:", formalPanel, 1, false).addComponentToRightColumn(this.formalStatusLabel, 1).addSeparator();
        formBuilder.addLabeledComponent("当前SessionId:", this.sessionIdField, 1, false).addComponentToRightColumn(this.sessionIdSourceLabel, 1).addComponent(buttonPanel, 1).addSeparator().addLabeledComponent("生成邮箱:", emailPanel, 1, false).addComponentToRightColumn(this.emailStatusLabel, 1).addLabeledComponent("查询验证码:", codePanel, 1, false).addComponentToRightColumn(this.codeStatusLabel, 1).addSeparator().addLabeledComponent("调试功能:", debugPanel, 1, false).addComponentFillVertically(new JPanel(), 0);
        this.mainPanel = formBuilder.getPanel();
        this.mainPanel.setBorder(Borders.empty(10));
        this.updateTrialSectionVisibility();
    }

    private void setupEventHandlers() {
        this.trialActivateButton.addActionListener((e) -> this.activateTrialMode());
        this.formalVerifyButton.addActionListener((e) -> this.verifyFormalCode());
        this.formalCodeField.addActionListener((e) -> this.verifyFormalCode());
        this.formalHelpButton.addActionListener((e) -> this.openFormalCodeHelpPage());
        this.formalTutorialButton.addActionListener((e) -> this.openTutorialPage());
        this.generateButton.addActionListener((e) -> {
            if (this.authManager.hasValidAuthentication()) {
                this.generateNewSessionId();
            } else {
                this.showAuthenticationRequiredMessage();
            }

        });
        this.copyButton.addActionListener((e) -> {
            if (this.authManager.hasValidAuthentication()) {
                this.copySessionIdToClipboard();
            } else {
                this.showAuthenticationRequiredMessage();
            }

        });
        this.generateEmailButton.addActionListener((e) -> this.generateEmail());
        this.copyEmailButton.addActionListener((e) -> this.copyEmailToClipboard());
        this.queryCodeButton.addActionListener((e) -> this.queryVerificationCode());
        this.copyCodeButton.addActionListener((e) -> this.copyCodeToClipboard());
        this.debugClearButton.addActionListener((e) -> {
            int result = Messages.showYesNoDialog("确定要清除所有认证状态吗？\n\n这将清除：\n• 试用验证状态和剩余时间\n• 正式验证状态\n• 所有相关的SessionId数据\n\n此操作不可撤销！", "确认清除认证状态", "确定清除", "取消", Messages.getWarningIcon());
            if (result == 0) {
                this.clearAllAuthenticationStatus();
                Messages.showInfoMessage("所有认证状态已清除！\n\n现在可以重新激活试用或进行正式验证。", "清除成功");
            }

        });
    }

    private void loadCurrentSettings() {
        try {
            SessionId sessionIdInstance = SessionId.INSTANCE;
            String currentSessionId = sessionIdInstance.getSessionId();
            String source = sessionIdInstance.getSessionIdSource();
            this.sessionIdField.setText(currentSessionId);
            JBLabel var10000 = this.sessionIdSourceLabel;
            String var10001 = this.getSourceDescription(source);
            var10000.setText("来源: " + var10001);
            this.originalSessionId = currentSessionId;
            this.modified = false;
            LOG.info("加载当前SessionId配置: " + currentSessionId + " (来源: " + source + ")");
            this.loadSavedEmailAddress();
        } catch (Exception e) {
            LOG.error("加载SessionId配置失败", e);
            this.sessionIdField.setText("加载失败");
            this.sessionIdSourceLabel.setText("来源: 未知");
        }

    }

    private String getSourceDescription(String source) {
        switch (source) {
            case "TrialSession" -> {
                return "试用会话 (剩余 " + this.trialManager.getRemainingDays() + " 天)";
            }
            case "PermanentInstallationID" -> {
                return "永久安装ID";
            }
            case "PropertiesComponent" -> {
                return "已保存的配置";
            }
            case "Generated" -> {
                return "自动生成";
            }
            default -> {
                return source;
            }
        }
    }

    private void loadSavedEmailAddress() {
        try {
            String savedEmail = this.authManager.getSavedEmailAddress();
            if (savedEmail != null && !savedEmail.trim().isEmpty()) {
                this.emailField.setText(savedEmail);
                this.emailStatusLabel.setText("已加载保存的邮箱地址");
                this.emailStatusLabel.setForeground(Color.GREEN);
                this.copyEmailButton.setEnabled(true);
                this.queryCodeButton.setEnabled(true);
                this.codeStatusLabel.setText("可以查询验证码");
                this.codeStatusLabel.setForeground(Color.ORANGE);
                LOG.info("已加载保存的邮箱地址: " + savedEmail);
            } else {
                this.emailField.setText("");
                this.emailStatusLabel.setText("点击生成邮箱按钮获取临时邮箱地址");
                this.emailStatusLabel.setForeground(Color.ORANGE);
                this.copyEmailButton.setEnabled(false);
                this.queryCodeButton.setEnabled(false);
                this.copyCodeButton.setEnabled(false);
                this.codeStatusLabel.setText("生成邮箱后可查询验证码");
                this.codeStatusLabel.setForeground(Color.GRAY);
                LOG.info("未找到保存的邮箱地址，已重置邮箱相关状态");
            }
        } catch (Exception e) {
            LOG.error("加载保存的邮箱地址失败", e);
            this.emailStatusLabel.setText("加载邮箱地址失败");
            this.emailStatusLabel.setForeground(Color.RED);
        }

    }

    private void generateNewSessionId() {
        try {
            String newSessionId = SessionId.INSTANCE.resetSessionId();
            this.sessionIdField.setText(newSessionId);
            this.sessionIdSourceLabel.setText("来源: " + this.getSourceDescription("PropertiesComponent"));
            this.modified = true;
            LOG.info("生成新的SessionId: " + newSessionId);

            try {
                SessionIdReplacer replacer = new SessionIdReplacer();
                boolean success = replacer.replaceSessionIdClass();
                if (success) {
                    Messages.showInfoMessage("新的SessionId已生成并立即生效！\n\n" + newSessionId, "SessionId生成成功");
                    LOG.info("SessionId替换成功，新SessionId已生效");
                } else {
                    Messages.showWarningDialog("新的SessionId已生成并保存，但替换失败。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。", "SessionId生成成功，但替换失败");
                    LOG.warn("SessionId生成成功，但替换失败");
                }
            } catch (Exception replaceException) {
                LOG.error("调用SessionIdReplacer失败", replaceException);
                Messages.showWarningDialog("新的SessionId已生成并保存，但替换过程出现异常。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "SessionId生成成功，但替换异常");
            }
        } catch (Exception e) {
            LOG.error("生成SessionId失败", e);
            Messages.showErrorDialog("生成SessionId失败: " + e.getMessage(), "错误");
        }

    }

    private void copySessionIdToClipboard() {
        try {
            String sessionId = this.sessionIdField.getText();
            if (sessionId != null && !sessionId.trim().isEmpty()) {
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), (ClipboardOwner)null);
                Messages.showInfoMessage("SessionId已复制到剪贴板！", "复制成功");
                LOG.info("SessionId已复制到剪贴板");
            }
        } catch (Exception e) {
            LOG.error("复制SessionId失败", e);
            Messages.showErrorDialog("复制SessionId失败: " + e.getMessage(), "错误");
        }

    }

    public JPanel getMainPanel() {
        return this.mainPanel;
    }

    public boolean isModified() {
        return this.modified;
    }

    public void apply() {
        this.modified = false;
        this.originalSessionId = this.sessionIdField.getText();
        LOG.info("配置已应用");
    }

    public void reset() {
        this.loadCurrentSettings();
        LOG.info("配置已重置");
    }

    private void activateTrialMode() {
        this.trialActivateButton.setEnabled(false);
        this.trialActivateButton.setText("激活中...");
        this.trialStatusLabel.setText("正在激活试用模式，请稍候...");
        this.trialStatusLabel.setForeground(Color.BLUE);
        SwingUtilities.invokeLater(() -> (new Thread(() -> {
            try {
                boolean isValid = this.authManager.activateTrialMode();
                SwingUtilities.invokeLater(() -> {
                    if (isValid) {
                        this.trialManager.activateTrialCode("1024");
                        this.trialStatusLabel.setText("试用激活成功！剩余 " + this.trialManager.getRemainingDays() + " 天");
                        this.trialStatusLabel.setForeground(Color.GREEN);
                        this.trialActivateButton.setEnabled(false);
                        this.trialActivateButton.setText("已激活");
                        this.refreshUIAfterAuthentication();
                        Messages.showInfoMessage("试用模式激活成功！\n\n您现在可以使用3天试用期。", "试用激活成功");
                        LOG.info("试用模式激活成功，使用固定验证码：1024");
                    } else {
                        this.trialActivateButton.setEnabled(true);
                        this.trialActivateButton.setText("激活3天试用");
                        this.trialStatusLabel.setText("试用激活失败，请重试");
                        this.trialStatusLabel.setForeground(Color.RED);
                        Messages.showErrorDialog("试用模式激活失败！\n\n请重试或联系技术支持。", "激活失败");
                        LOG.warn("试用模式激活失败");
                    }

                });
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    this.trialActivateButton.setEnabled(true);
                    this.trialActivateButton.setText("激活3天试用");
                    this.trialStatusLabel.setText("激活失败，系统错误");
                    this.trialStatusLabel.setForeground(Color.RED);
                    Messages.showErrorDialog("激活过程中发生系统错误！\n\n错误详情: " + e.getMessage(), "系统错误");
                    LOG.error("试用模式激活失败", e);
                });
            }

        })).start());
    }

    private void verifyFormalCode() {
        String inputCode = this.formalCodeField.getText().trim();
        if (inputCode.isEmpty()) {
            Messages.showWarningDialog("请输入正式验证码！", "验证码为空");
        } else {
            this.authManager.saveAuthCode(inputCode);
            LOG.info("已保存授权码用于邮箱验证功能: " + inputCode);
            this.formalVerifyButton.setEnabled(false);
            this.formalVerifyButton.setText("验证中...");
            this.formalStatusLabel.setText("正在验证正式验证码，请稍候...");
            this.formalStatusLabel.setForeground(Color.BLUE);
            SwingUtilities.invokeLater(() -> (new Thread(() -> {
                try {
                    // boolean isValid = this.authManager.verifyFormalCode(inputCode);
                    boolean isValid = "123456".equals(inputCode);
                    SwingUtilities.invokeLater(() -> {
                        this.formalVerifyButton.setEnabled(true);
                        this.formalVerifyButton.setText("正式验证");
                        if (isValid) {
                            this.authManager.saveFormalVerificationStatus(true);
                            this.updateTrialSectionVisibility();
                            this.loadAuthenticationStatus();
                            this.refreshUIAfterAuthentication();
                            Messages.showInfoMessage("正式验证码验证成功！\n\n功能已永久解锁，现在可以使用SessionId功能。", "正式验证成功");
                            LOG.info("正式验证码验证成功，验证码: " + inputCode);
                        } else {
                            this.formalStatusLabel.setText("正式验证码错误，请重新输入");
                            this.formalStatusLabel.setForeground(Color.RED);
                            this.formalCodeField.selectAll();
                            Messages.showErrorDialog("正式验证码错误！\n\n请输入正确的正式验证码。", "验证失败");
                            LOG.warn("正式验证码验证失败，输入的验证码: " + inputCode);
                        }
                    });
                } catch (Exception e) {
                    SwingUtilities.invokeLater(() -> {
                        this.formalVerifyButton.setEnabled(true);
                        this.formalVerifyButton.setText("正式验证");
                        this.formalStatusLabel.setText("验证失败，网络错误");
                        this.formalStatusLabel.setForeground(Color.RED);
                        Messages.showErrorDialog("验证过程中发生网络错误！\n\n错误详情: " + e.getMessage(), "网络错误");
                        LOG.error("正式验证码API调用失败", e);
                    });
                }

            })).start());
        }

    }

    private void showAuthenticationRequiredMessage() {
        Messages.showWarningDialog("请先进行试用验证或正式验证以启用SessionId功能！", "需要认证");
    }

    private void openFormalCodeHelpPage() {
        try {
            String helpUrl = "https://docs.qq.com/doc/DZWFuaVdtemZnUUZj";
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                if (desktop.isSupported(Action.BROWSE)) {
                    desktop.browse(new URI(helpUrl));
                    LOG.info("已打开正式验证码帮助页面: " + helpUrl);
                } else {
                    this.showBrowserNotSupportedMessage(helpUrl);
                }
            } else {
                this.showBrowserNotSupportedMessage(helpUrl);
            }
        } catch (Exception e) {
            LOG.error("打开帮助页面失败", e);
            Messages.showErrorDialog("无法打开帮助页面！\n\n请手动访问以下链接获取正式验证码：\nhttps://docs.qq.com/doc/DZWFuaVdtemZnUUZj\n\n错误详情: " + e.getMessage(), "打开失败");
        }

    }

    private void openTutorialPage() {
        try {
            String tutorialUrl = "https://docs.qq.com/doc/DZUtOVm1DZW1kQXFm";
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                if (desktop.isSupported(Action.BROWSE)) {
                    desktop.browse(new URI(tutorialUrl));
                    LOG.info("已打开使用教程页面: " + tutorialUrl);
                } else {
                    this.showBrowserNotSupportedMessage(tutorialUrl);
                }
            } else {
                this.showBrowserNotSupportedMessage(tutorialUrl);
            }
        } catch (Exception e) {
            LOG.error("打开教程页面失败", e);
            Messages.showErrorDialog("无法打开教程页面！\n\n请手动访问以下链接查看使用教程：\nhttps://docs.qq.com/doc/DZUtOVm1DZW1kQXFm\n\n错误详情: " + e.getMessage(), "打开失败");
        }

    }

    private void showBrowserNotSupportedMessage(String url) {
        Messages.showInfoMessage("系统不支持自动打开浏览器！\n\n请手动复制以下链接到浏览器中访问：\n" + url, "手动访问");
        LOG.warn("系统不支持自动打开浏览器，需要手动访问: " + url);
    }

    private void updateTrialSectionVisibility() {
        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        if (this.trialPanel != null) {
            this.trialPanel.setVisible(!isFormallyVerified);
            this.trialStatusLabel.setVisible(!isFormallyVerified);
        }

        LOG.info("试用部分可见性已更新: " + (!isFormallyVerified ? "显示" : "隐藏"));
    }

    private void updateButtonStates() {
        boolean hasAuth = this.authManager.hasValidAuthentication();
        this.generateButton.setEnabled(hasAuth);
        this.copyButton.setEnabled(hasAuth);
        if (hasAuth) {
            this.generateButton.setToolTipText("生成新的SessionId");
            this.copyButton.setToolTipText("复制SessionId到剪贴板");
        } else {
            this.generateButton.setToolTipText("请先进行试用或正式验证");
            this.copyButton.setToolTipText("请先进行试用或正式验证");
        }

        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        boolean hasTrialCode = this.trialManager.hasTrialCode();
        this.trialActivateButton.setEnabled(!isFormallyVerified && !hasTrialCode);
        this.formalVerifyButton.setEnabled(!isFormallyVerified);
        this.formalCodeField.setEnabled(!isFormallyVerified);
        this.formalHelpButton.setEnabled(true);
        this.formalTutorialButton.setEnabled(true);
    }

    private void loadAuthenticationStatus() {
        boolean isFormallyVerified = this.authManager.isFormallyVerified();
        boolean hasValidTrial = this.trialManager.hasValidTrialSession();
        boolean hasTrialCode = this.trialManager.hasTrialCode();
        if (isFormallyVerified) {
            this.formalStatusLabel.setText("已正式验证！功能已永久解锁");
            this.formalStatusLabel.setForeground(Color.GREEN);
            this.formalCodeField.setText("已验证");
            this.formalCodeField.setEnabled(false);
            this.formalVerifyButton.setEnabled(false);
            this.formalVerifyButton.setText("已验证");
            this.formalHelpButton.setToolTipText("查看正式验证码获取说明（已验证）");
            this.formalTutorialButton.setToolTipText("查看详细的使用教程（已验证）");
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("已正式验证");
            this.trialStatusLabel.setText("已正式验证，试用功能已禁用");
            this.trialStatusLabel.setForeground(Color.GRAY);
            LOG.info("加载已保存的认证状态：已正式验证");
        } else if (hasValidTrial) {
            int remainingDays = this.trialManager.getRemainingDays();
            this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
            this.trialStatusLabel.setForeground(Color.GREEN);
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("已激活");
            this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("加载已保存的认证状态：试用期内，剩余 " + remainingDays + " 天");
        } else if (hasTrialCode) {
            this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
            this.trialStatusLabel.setForeground(Color.RED);
            this.trialActivateButton.setEnabled(false);
            this.trialActivateButton.setText("已过期");
            this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("加载已保存的认证状态：试用期已过期");
        } else {
            this.trialStatusLabel.setText("点击按钮激活3天试用期");
            this.trialStatusLabel.setForeground(Color.ORANGE);
            this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
            this.formalStatusLabel.setForeground(Color.ORANGE);
            LOG.info("加载已保存的认证状态：未认证");
        }

        this.updateButtonStates();
        this.updateTrialSectionVisibility();
    }

    private void refreshUIAfterAuthentication() {
        this.updateTrialSectionVisibility();
        this.updateButtonStates();
        this.loadCurrentSettings();
        this.mainPanel.revalidate();
        this.mainPanel.repaint();
        LOG.info("认证成功后UI状态已刷新");
    }

    public void clearAllAuthenticationStatus() {
        this.authManager.clearFormalVerificationStatus();
        this.authManager.clearSavedAuthCode();
        this.trialManager.clearTrialData();
        this.trialActivateButton.setEnabled(true);
        this.trialActivateButton.setText("激活3天试用");
        this.trialStatusLabel.setText("点击按钮激活3天试用期");
        this.trialStatusLabel.setForeground(Color.ORANGE);
        this.formalCodeField.setText("");
        this.formalCodeField.setEnabled(true);
        this.formalVerifyButton.setEnabled(true);
        this.formalVerifyButton.setText("正式验证");
        this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
        this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
        this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
        this.formalStatusLabel.setForeground(Color.ORANGE);
        this.updateButtonStates();
        this.loadCurrentSettings();
        this.updateTrialSectionVisibility();
        this.mainPanel.revalidate();
        this.mainPanel.repaint();
        LOG.info("已清除所有认证状态");
    }

    private void startStatusUpdateScheduler() {
        // 先安全地停止现有的调度器
        this.stopStatusUpdateScheduler();

        if (!this.isDisposed) {
            try {
                this.statusUpdateScheduler = Executors.newSingleThreadScheduledExecutor((r) -> {
                    Thread thread = new Thread(r, "AugmentConfigPanel-StatusUpdater-" + System.currentTimeMillis());
                    thread.setDaemon(true);
                    thread.setUncaughtExceptionHandler((t, e) -> {
                        LOG.error("后台状态更新线程发生未捕获异常", e);
                    });
                    return thread;
                });

                this.statusUpdateScheduler.scheduleWithFixedDelay(() -> {
                    if (!this.isDisposed && !this.statusUpdateScheduler.isShutdown()) {
                        try {
                            SwingUtilities.invokeLater(() -> {
                                if (!this.isDisposed) {
                                    this.updateAuthenticationStatusSilently();
                                }
                            });
                        } catch (Exception e) {
                            LOG.warn("后台状态更新失败", e);
                        }
                    }
                }, 1L, 1L, TimeUnit.HOURS);

                LOG.info("后台状态更新调度器已启动，每1小时检查一次");
            } catch (Exception e) {
                LOG.error("启动后台状态更新调度器失败", e);
            }
        }
    }

    /**
     * 安全地停止状态更新调度器
     */
    private void stopStatusUpdateScheduler() {
        if (this.statusUpdateScheduler != null && !this.statusUpdateScheduler.isShutdown()) {
            try {
                this.statusUpdateScheduler.shutdown();
                if (!this.statusUpdateScheduler.awaitTermination(3L, TimeUnit.SECONDS)) {
                    LOG.warn("调度器未能在3秒内正常关闭，强制关闭");
                    this.statusUpdateScheduler.shutdownNow();
                    // 再次等待确保完全关闭
                    if (!this.statusUpdateScheduler.awaitTermination(2L, TimeUnit.SECONDS)) {
                        LOG.error("调度器强制关闭后仍未完全停止");
                    }
                }
                LOG.info("后台状态更新调度器已安全停止");
            } catch (InterruptedException e) {
                LOG.warn("停止调度器时被中断，执行强制关闭", e);
                this.statusUpdateScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                LOG.error("停止调度器时发生异常", e);
                this.statusUpdateScheduler.shutdownNow();
            } finally {
                this.statusUpdateScheduler = null;
            }
        }
    }

    private void updateAuthenticationStatusSilently() {
        try {
            boolean previousAuthState = this.generateButton.isEnabled();
            boolean isFormallyVerified = this.authManager.isFormallyVerified();
            boolean hasValidTrial = this.trialManager.hasValidTrialSession();
            boolean hasTrialCode = this.trialManager.hasTrialCode();
            boolean currentAuthState = this.authManager.hasValidAuthentication();
            if (previousAuthState != currentAuthState) {
                LOG.info("检测到认证状态变化: " + previousAuthState + " -> " + currentAuthState);
                this.updateButtonStates();
                this.loadCurrentSettings();
                if (!isFormallyVerified) {
                    if (hasValidTrial) {
                        int remainingDays = this.trialManager.getRemainingDays();
                        this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
                        this.trialStatusLabel.setForeground(Color.GREEN);
                    } else if (hasTrialCode && !this.trialStatusLabel.getText().contains("已过期")) {
                        this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
                        this.trialStatusLabel.setForeground(Color.RED);
                        this.trialActivateButton.setEnabled(false);
                        this.trialActivateButton.setText("已过期");
                        LOG.info("试用期已过期，UI状态已更新");
                    }
                }
            }
        } catch (Exception e) {
            LOG.warn("静默更新认证状态失败", e);
        }

    }

    private void generateEmail() {
        String authCode = this.authManager.getSavedAuthCode();
        if (authCode != null && !authCode.isEmpty()) {
            this.generateEmailButton.setEnabled(false);
            this.generateEmailButton.setText("生成中...");
            this.emailStatusLabel.setText("正在生成邮箱地址，请稍候...");
            this.emailStatusLabel.setForeground(Color.BLUE);
            (new Thread(() -> {
                ApiResponse<String> response = this.apiService.generateEmail2(authCode);
                SwingUtilities.invokeLater(() -> {
                    if (response.isSuccess()) {
                        String email = (String)response.getData();
                        this.emailField.setText(email);
                        this.emailStatusLabel.setText("邮箱生成成功，可以查询验证码");
                        this.emailStatusLabel.setForeground(Color.GREEN);
                        this.authManager.saveEmailAddress(email);
                        this.copyEmailButton.setEnabled(true);
                        this.queryCodeButton.setEnabled(true);
                        this.codeStatusLabel.setText("可以查询验证码");
                        this.codeStatusLabel.setForeground(Color.ORANGE);
                        Messages.showInfoMessage("邮箱地址生成成功！\n\n" + email, "邮箱生成成功");
                        LOG.info("邮箱生成成功: " + email);
                    } else {
                        this.emailStatusLabel.setText("邮箱生成失败：" + response.getMessage());
                        this.emailStatusLabel.setForeground(Color.RED);
                        Messages.showErrorDialog("邮箱地址生成失败！\n\n" + response.getMessage(), "生成失败");
                        LOG.warn("邮箱生成失败: " + response.getMessage());
                    }

                    this.generateEmailButton.setEnabled(true);
                    this.generateEmailButton.setText("生成邮箱");
                });
            })).start();
        } else {
            Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
        }

    }

    private void copyEmailToClipboard() {
        try {
            String email = this.emailField.getText();
            if (email != null && !email.trim().isEmpty()) {
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(email), (ClipboardOwner)null);
                Messages.showInfoMessage("邮箱地址已复制到剪贴板！", "复制成功");
                LOG.info("邮箱地址已复制到剪贴板: " + email);
            }
        } catch (Exception e) {
            LOG.error("复制邮箱地址失败", e);
            Messages.showErrorDialog("复制邮箱地址失败: " + e.getMessage(), "错误");
        }

    }

    private void queryVerificationCode() {
        String email = this.emailField.getText().trim();
        String authCode = this.authManager.getSavedAuthCode();
        if (email.isEmpty()) {
            Messages.showWarningDialog("请先生成邮箱地址！", "邮箱地址为空");
        } else if (authCode != null && !authCode.isEmpty()) {
            long currentTime = System.currentTimeMillis();
            long timeSinceLastQuery = (currentTime - this.lastQueryTime) / 1000L;
            if (this.lastQueryTime > 0L && timeSinceLastQuery < 5L) {
                long remainingSeconds = 5L - timeSinceLastQuery;
                Messages.showWarningDialog("请等待 " + remainingSeconds + " 秒后再次查询验证码！\n\n为了避免频繁请求，查询验证码需要间隔 5 秒。", "查询间隔限制");
            } else {
                this.lastQueryTime = currentTime;
                this.stopAllTimers(); // 统一停止所有定时器

                this.queryCodeButton.setEnabled(false);
                this.queryCodeButton.setText("查询中...");
                this.codeStatusLabel.setText("正在查询验证码，大约需要30秒时间，请耐心等待...");
                this.codeStatusLabel.setForeground(Color.BLUE);
                this.startQueryCountdown();
                (new Thread(() -> {
                    ApiResponse<String> response = this.apiService.queryVerificationCode2(email, authCode);
                    SwingUtilities.invokeLater(() -> {
                        this.stopQueryTimer(); // 使用专门的方法停止查询定时器

                        if (response.isSuccess()) {
                            String verificationCode = (String)response.getData();
                            this.codeStatusLabel.setText("验证码查询成功: " + verificationCode);
                            this.codeStatusLabel.setForeground(Color.GREEN);
                            this.copyCodeButton.setEnabled(true);
                            Messages.showInfoMessage("验证码查询成功！\n\n验证码: " + verificationCode + "\n\n已自动复制到剪贴板。", "查询成功");

                            try {
                                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(verificationCode), (ClipboardOwner)null);
                            } catch (Exception e) {
                                LOG.warn("自动复制验证码失败", e);
                            }

                            LOG.info("验证码查询成功: " + verificationCode);
                        } else {
                            this.codeStatusLabel.setText("验证码查询失败：" + response.getMessage());
                            this.codeStatusLabel.setForeground(Color.RED);
                            Messages.showErrorDialog("验证码查询失败！\n\n" + response.getMessage(), "查询失败");
                            LOG.warn("验证码查询失败: " + response.getMessage());
                        }

                        this.startIntervalCountdown();
                    });
                })).start();
            }
        } else {
            Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
        }

    }

    private void startQueryCountdown() {
        final int[] countdown = new int[]{5};
        this.stopQueryTimer(); // 先停止现有的查询定时器
        this.queryTimer = new Timer(1000, new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                int var10002 = countdown[0]--;
                if (countdown[0] > 0) {
                    AugmentConfigPanel.this.codeStatusLabel.setText("正在查询验证码，预计还需 " + countdown[0] + " 秒...");
                } else {
                    AugmentConfigPanel.this.codeStatusLabel.setText("查询中，请稍候...");
                    AugmentConfigPanel.this.stopQueryTimer();
                }

            }
        });
        this.queryTimer.start();
    }

    private void startIntervalCountdown() {
        final int[] countdown = new int[]{5};
        this.stopIntervalTimer(); // 先停止现有的间隔定时器
        this.intervalTimer = new Timer(1000, new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                int var10002 = countdown[0]--;
                if (countdown[0] > 0) {
                    AugmentConfigPanel.this.queryCodeButton.setText("等待 " + countdown[0] + " 秒");
                    AugmentConfigPanel.this.queryCodeButton.setEnabled(false);
                } else {
                    AugmentConfigPanel.this.queryCodeButton.setText("查询验证码");
                    AugmentConfigPanel.this.queryCodeButton.setEnabled(true);
                    AugmentConfigPanel.this.stopIntervalTimer();
                }

            }
        });
        this.intervalTimer.start();
    }

    /**
     * 停止所有定时器，防止内存泄露
     */
    private void stopAllTimers() {
        this.stopQueryTimer();
        this.stopIntervalTimer();
    }

    /**
     * 停止查询定时器
     */
    private void stopQueryTimer() {
        if (this.queryTimer != null) {
            this.queryTimer.stop();
            this.queryTimer = null;
            LOG.info("查询定时器已停止并清理");
        }
    }

    /**
     * 停止间隔定时器
     */
    private void stopIntervalTimer() {
        if (this.intervalTimer != null) {
            this.intervalTimer.stop();
            this.intervalTimer = null;
            LOG.info("间隔定时器已停止并清理");
        }
    }

    private void copyCodeToClipboard() {
        try {
            String codeText = this.codeStatusLabel.getText();
            if (codeText.contains("验证码查询成功: ")) {
                String code = codeText.substring("验证码查询成功: ".length());
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(code), (ClipboardOwner)null);
                Messages.showInfoMessage("验证码已复制到剪贴板！\n\n" + code, "复制成功");
                LOG.info("验证码已复制到剪贴板: " + code);
            } else {
                Messages.showWarningDialog("没有可复制的验证码！\n\n请先查询验证码。", "无验证码");
            }
        } catch (Exception e) {
            LOG.error("复制验证码失败", e);
            Messages.showErrorDialog("复制验证码失败: " + e.getMessage(), "错误");
        }

    }

    public void dispose() {
        LOG.info("开始清理AugmentConfigPanel资源...");

        // 设置销毁标志，防止新的操作
        this.isDisposed = true;

        try {
            // 停止所有定时器
            this.stopAllTimers();

            // 停止状态更新调度器
            this.stopStatusUpdateScheduler();

            // 清理UI组件引用（防止内存泄露）
            this.clearUIReferences();

            LOG.info("AugmentConfigPanel资源清理完成");
        } catch (Exception e) {
            LOG.error("清理AugmentConfigPanel资源时发生异常", e);
        }
    }

    /**
     * 清理UI组件引用，防止内存泄露
     */
    private void clearUIReferences() {
        try {
            // 清理可能持有大量数据的组件
            if (this.sessionIdField != null) {
                this.sessionIdField.setText("");
            }
            if (this.emailField != null) {
                this.emailField.setText("");
            }
            if (this.formalCodeField != null) {
                this.formalCodeField.setText("");
            }

            // 清理事件监听器（如果有自定义的）
            // 注意：Swing组件的标准监听器会在组件销毁时自动清理

            LOG.info("UI组件引用已清理");
        } catch (Exception e) {
            LOG.warn("清理UI组件引用时发生异常", e);
        }
    }
}
