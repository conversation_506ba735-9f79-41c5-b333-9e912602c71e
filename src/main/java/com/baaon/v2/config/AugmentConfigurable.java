//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2.config;

import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.options.ConfigurationException;
import com.intellij.openapi.util.NlsContexts.ConfigurableName;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nls.Capitalization;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;

public class AugmentConfigurable implements Configurable {
    private AugmentConfigPanel configPanel;

    public @Nls(
            capitalization = Capitalization.Title
    ) @ConfigurableName String getDisplayName() {
        return "Augment Assistant";
    }

    public @Nullable JComponent createComponent() {
        if (this.configPanel == null) {
            this.configPanel = new AugmentConfigPanel();
        }

        return this.configPanel.getMainPanel();
    }

    public boolean isModified() {
        return this.configPanel != null && this.configPanel.isModified();
    }

    public void apply() throws ConfigurationException {
        if (this.configPanel != null) {
            this.configPanel.apply();
        }

    }

    public void reset() {
        if (this.configPanel != null) {
            this.configPanel.reset();
        }

    }

    public void disposeUIResources() {
        if (this.configPanel != null) {
            this.configPanel.dispose();
            this.configPanel = null;
        }

    }
}
