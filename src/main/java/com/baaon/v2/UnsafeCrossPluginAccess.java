//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Field;
import java.util.Map;

public class UnsafeCrossPluginAccess {
    private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);

    public static Object tryGetService(String pluginId, String serviceClassName) {
        try {
            Application app = ApplicationManager.getApplication();
            Field servicesField = app.getClass().getDeclaredField("myServices");
            servicesField.setAccessible(true);
            Map<?, ?> services = (Map)servicesField.get(app);

            for(Map.Entry<?, ?> entry : services.entrySet()) {
                Object service = entry.getValue();
                String var10001 = service != null ? service.getClass().getName() : "none";
                LOG.info("services: " + var10001);
                if (service != null && service.getClass().getName().equals(serviceClassName)) {
                    return service;
                }
            }
        } catch (Exception e) {
            LOG.error("获取Service异常");
            e.printStackTrace();
        }

        return null;
    }
}
