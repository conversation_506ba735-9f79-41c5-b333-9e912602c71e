//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManager;
import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class SessionIdReplacer {
    private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);

    // 缓存反射结果，避免重复查找
    private static volatile ClassLoader cachedTargetClassLoader = null;
    private static volatile Class<?> cachedApiImplClass = null;
    private static volatile Class<?> cachedHttpClientClass = null;
    private static volatile Constructor<?> cachedHttpClientConstructor = null;
    private static volatile long lastCacheTime = 0;
    private static final long CACHE_VALIDITY_PERIOD = 300000; // 5分钟缓存有效期
    private static final Object cacheLock = new Object();

    private boolean reinitializeHttpClient() {
        try {
            ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
            ClassLoader targetClassLoader = getTargetClassLoader();

            if (targetClassLoader == null) {
                LOG.error("无法获取目标插件类加载器");
                return false;
            }

            Thread.currentThread().setContextClassLoader(targetClassLoader);

            // 使用缓存的反射结果
            ReflectionCache cache = getReflectionCache(targetClassLoader);
            if (cache == null) {
                LOG.error("无法获取反射缓存");
                return false;
            }

            Application app = ApplicationManager.getApplication();
            Method method = app.getClass().getMethod("getService", Class.class);
            Object invoke = method.invoke(app, cache.apiImplClass);
            Field httpClientField = invoke.getClass().getDeclaredField("httpClient");
            httpClientField.setAccessible(true);
            String sessionId = SessionId.INSTANCE.getSessionId();
            LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.getSessionIdSource() + ")");
            Object newHttpClient = cache.httpClientConstructor.newInstance(sessionId);
            httpClientField.set(invoke, newHttpClient);
            LOG.info("成功重新初始化httpClient实例");
            Thread.currentThread().setContextClassLoader(originalClassLoader);
            return true;
        } catch (Exception e) {
            LOG.error("重新初始化httpClient实例失败", e);
            // 清除缓存，下次重新获取
            clearReflectionCache();
            return false;
        }
    }

    /**
     * 获取目标插件的类加载器，使用缓存优化性能
     */
    private ClassLoader getTargetClassLoader() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否有效
        if (cachedTargetClassLoader != null &&
            (currentTime - lastCacheTime) < CACHE_VALIDITY_PERIOD) {
            LOG.debug("使用缓存的类加载器");
            return cachedTargetClassLoader;
        }

        synchronized (cacheLock) {
            // 双重检查
            if (cachedTargetClassLoader != null &&
                (currentTime - lastCacheTime) < CACHE_VALIDITY_PERIOD) {
                return cachedTargetClassLoader;
            }

            try {
                PluginManager pluginManager = PluginManager.getInstance();
                IdeaPluginDescriptor targetPlugin = null;
                IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();

                for(IdeaPluginDescriptor plugin : allPlugins) {
                    if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
                        targetPlugin = plugin;
                        break;
                    }
                }

                if (targetPlugin != null) {
                    cachedTargetClassLoader = targetPlugin.getPluginClassLoader();
                    lastCacheTime = currentTime;
                    LOG.info("成功获取并缓存目标插件的类加载器");
                    return cachedTargetClassLoader;
                }
            } catch (Exception e) {
                LOG.warn("无法获取目标插件类加载器: " + e.getMessage());
            }

            // 使用当前类加载器作为后备
            cachedTargetClassLoader = this.getClass().getClassLoader();
            lastCacheTime = currentTime;
            LOG.info("使用当前类加载器作为后备");
            return cachedTargetClassLoader;
        }
    }

    /**
     * 反射缓存内部类
     */
    private static class ReflectionCache {
        final Class<?> apiImplClass;
        final Class<?> httpClientClass;
        final Constructor<?> httpClientConstructor;

        ReflectionCache(Class<?> apiImplClass, Class<?> httpClientClass, Constructor<?> httpClientConstructor) {
            this.apiImplClass = apiImplClass;
            this.httpClientClass = httpClientClass;
            this.httpClientConstructor = httpClientConstructor;
        }
    }

    /**
     * 获取反射缓存，如果缓存无效则重新创建
     */
    private ReflectionCache getReflectionCache(ClassLoader targetClassLoader) {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否有效
        if (cachedApiImplClass != null && cachedHttpClientClass != null &&
            cachedHttpClientConstructor != null &&
            (currentTime - lastCacheTime) < CACHE_VALIDITY_PERIOD) {
            LOG.debug("使用缓存的反射结果");
            return new ReflectionCache(cachedApiImplClass, cachedHttpClientClass, cachedHttpClientConstructor);
        }

        synchronized (cacheLock) {
            // 双重检查
            if (cachedApiImplClass != null && cachedHttpClientClass != null &&
                cachedHttpClientConstructor != null &&
                (currentTime - lastCacheTime) < CACHE_VALIDITY_PERIOD) {
                return new ReflectionCache(cachedApiImplClass, cachedHttpClientClass, cachedHttpClientConstructor);
            }

            try {
                // 重新加载反射结果
                cachedApiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);
                cachedHttpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient", true, targetClassLoader);
                cachedHttpClientConstructor = cachedHttpClientClass.getConstructor(String.class);
                lastCacheTime = currentTime;

                LOG.info("成功缓存反射结果");
                return new ReflectionCache(cachedApiImplClass, cachedHttpClientClass, cachedHttpClientConstructor);
            } catch (Exception e) {
                LOG.error("创建反射缓存失败", e);
                return null;
            }
        }
    }

    /**
     * 清除反射缓存
     */
    private static void clearReflectionCache() {
        synchronized (cacheLock) {
            cachedTargetClassLoader = null;
            cachedApiImplClass = null;
            cachedHttpClientClass = null;
            cachedHttpClientConstructor = null;
            lastCacheTime = 0;
            LOG.info("反射缓存已清除");
        }
    }

    public boolean replaceSessionIdClass() {
        try {
            if (this.reinitializeHttpClient()) {
                return true;
            } else {
                LOG.warn("所有替换方法都失败");
                return false;
            }
        } catch (Exception e) {
            LOG.error("替换SessionId类时出错", e);
            return false;
        }
    }
}
