//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManager;
import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class SessionIdReplacer {
    private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);

    private boolean reinitializeHttpClient() {
        try {
            ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
            ClassLoader targetClassLoader = null;

            try {
                PluginManager pluginManager = PluginManager.getInstance();
                IdeaPluginDescriptor targetPlugin = null;
                IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();

                for(IdeaPluginDescriptor plugin : allPlugins) {
                    if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
                        targetPlugin = plugin;
                        break;
                    }
                }

                if (targetPlugin != null) {
                    targetClassLoader = targetPlugin.getPluginClassLoader();
                    LOG.info("成功获取目标插件的类加载器");
                }
            } catch (Exception e) {
                LOG.warn("无法获取目标插件类加载器，将使用当前类加载器: " + e.getMessage());
            }

            if (targetClassLoader == null) {
                targetClassLoader = this.getClass().getClassLoader();
            }

            Thread.currentThread().setContextClassLoader(targetClassLoader);
            Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);
            Application app = ApplicationManager.getApplication();
            Method method = app.getClass().getMethod("getService", Class.class);
            Object invoke = method.invoke(app, apiImplClass);
            Field httpClientField = invoke.getClass().getDeclaredField("httpClient");
            httpClientField.setAccessible(true);
            String sessionId = SessionId.INSTANCE.getSessionId();
            LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.getSessionIdSource() + ")");
            Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
            Constructor<?> constructor = httpClientClass.getConstructor(String.class);
            Object newHttpClient = constructor.newInstance(sessionId);
            httpClientField.set(invoke, newHttpClient);
            LOG.info("成功重新初始化httpClient实例");
            Thread.currentThread().setContextClassLoader(originalClassLoader);
            return true;
        } catch (Exception e) {
            LOG.error("重新初始化httpClient实例失败", e);
            return false;
        }
    }

    public boolean replaceSessionIdClass() {
        try {
            if (this.reinitializeHttpClient()) {
                return true;
            } else {
                LOG.warn("所有替换方法都失败");
                return false;
            }
        } catch (Exception e) {
            LOG.error("替换SessionId类时出错", e);
            return false;
        }
    }
}
