//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.baaon.v2;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class AugmentStartupActivity implements ProjectActivity {
    private static final Logger LOG = Logger.getInstance(AugmentStartupActivity.class);

    public @Nullable Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        LOG.info("开始替换目标插件类...");

        try {
            SessionIdReplacer replacer = new SessionIdReplacer();
            if (replacer.replaceSessionIdClass()) {
                LOG.info("成功替换SessionId类");
            } else {
                LOG.warn("替换SessionId类失败");
            }
        } catch (Exception e) {
            LOG.error("替换过程中发生错误", e);
        }

        return Unit.INSTANCE;
    }
}
